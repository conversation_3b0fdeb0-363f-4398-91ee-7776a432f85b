# PostgreSQL Database Setup for OnTheMove Admin

This guide will help you set up the PostgreSQL database for the OnTheMove Security Tracking Admin application.

## Prerequisites

1. **PostgreSQL** (version 12 or higher)
2. **Node.js** (version 16 or higher)
3. **npm** or **yarn**

## Installation Steps

### 1. Install PostgreSQL

#### Windows:
- Download PostgreSQL from https://www.postgresql.org/download/windows/
- Run the installer and follow the setup wizard
- Remember the password you set for the `postgres` user

#### macOS:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create a database user (optional)
createuser --interactive
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Database

Connect to PostgreSQL and create the database:

```bash
# Connect to PostgreSQL as postgres user
psql -U postgres

# Create the database
CREATE DATABASE onthemove;

# Create a dedicated user (optional but recommended)
CREATE USER onthemove_user WITH PASSWORD 'sysadmin';
GRANT ALL PRIVILEGES ON DATABASE onthemove TO onthemove_user;

# Exit psql
\q
```

### 3. Install Dependencies

Install the required Node.js packages:

```bash
npm install
```

### 4. Environment Configuration

Create a `.env` file in the root directory with the following variables:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=onthemove
DB_USER=postgres
DB_PASSWORD=sysadmin

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
```

**Important:** Replace `your_postgres_password` with your actual PostgreSQL password.

### 5. Start the Application

#### Option 1: Start Both Frontend and Backend
```bash
npm run start:dev
```

#### Option 2: Start Backend Only
```bash
npm run start:server
```

#### Option 3: Start Frontend Only (in separate terminal)
```bash
npm start
```

## Database Schema

The application will automatically create the following tables:

- **tenants** - Organizations/companies
- **users** - All system users (employees, admins, super admins)
- **locations** - Physical locations/sites
- **assignments** - User-location assignments with shifts
- **user_locations** - GPS tracking data
- **incidents** - Security incidents and reports
- **messages** - Internal messaging system
- **geofences** - Geographic boundaries
- **geofence_alerts** - Geofence violation alerts

## Initial Demo Data

The system will automatically create demo data on first run:

### Super Admin Account
- **Email:** `<EMAIL>`
- **Password:** `admin123`

### Demo Organization Admin
- **Email:** `<EMAIL>`
- **Password:** `admin123`

### Demo Employees
- **Email:** `<EMAIL>`, `<EMAIL>`, etc.
- **Password:** `employee123`

## API Endpoints

Once the server is running, you can access:

- **Health Check:** `http://localhost:3001/health`
- **API Base:** `http://localhost:3001/api`

### Main Endpoints:
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `GET /api/tenants` - Get all organizations (Super Admin only)
- `POST /api/tenants` - Create organization (Super Admin only)

## Troubleshooting

### Common Issues:

1. **Database Connection Error:**
   - Check PostgreSQL is running: `sudo service postgresql status`
   - Verify database credentials in `.env` file
   - Ensure database `onthemove_admin` exists

2. **Permission Errors:**
   - Grant permissions to your database user
   - Check PostgreSQL logs: `/var/log/postgresql/`

3. **Port Already in Use:**
   - Change PORT in `.env` file
   - Kill existing processes: `lsof -ti:3001 | xargs kill`

4. **Module Not Found:**
   - Run `npm install` to install dependencies
   - Clear npm cache: `npm cache clean --force`

### Useful Commands:

```bash
# Check PostgreSQL status
sudo service postgresql status

# Access PostgreSQL shell
psql -U postgres -d onthemove

# View database tables
\dt

# View table structure
\d table_name

# Reset database (careful - this deletes all data!)
DROP DATABASE onthemove;
CREATE DATABASE onthemove;
```

## Security Notes

- Change default passwords before production
- Use environment variables for sensitive data
- Enable PostgreSQL SSL in production
- Use strong JWT secrets
- Regularly backup your database

## Next Steps

1. Set up SSL certificates for production
2. Configure database backups
3. Set up monitoring and logging
4. Implement additional API endpoints as needed
5. Add database migrations for schema changes

For support, check the application logs or contact the development team. 