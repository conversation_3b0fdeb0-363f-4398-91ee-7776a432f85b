# Development Guide

This guide will help you set up the FootOnStreet development environment and contribute to the project.

## 🚀 Getting Started

### Prerequisites

Make sure you have the following installed:
- **Node.js** 16.x or later
- **npm** 7.x or later (or yarn)
- **PostgreSQL** 12.x or later
- **Git** for version control

### Environment Setup

1. **Clone the Repository**
```bash
git clone <repository-url>
cd onthemove-admin
```

2. **Install Dependencies**
```bash
npm install
```

3. **Environment Variables**
Create a `.env` file in the root directory:
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:
```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/footonstreet_dev
DB_HOST=localhost
DB_PORT=5432
DB_NAME=footonstreet_dev
DB_USER=your_username
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Server
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Mapbox
MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# File Upload
UPLOAD_MAX_SIZE=10mb
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,video/mp4

# Email (optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

4. **Database Setup**
```bash
# Create database
createdb footonstreet_dev

# Run migrations
npm run db:migrate

# Seed database (optional)
npm run db:seed
```

5. **Start Development Servers**
```bash
# Start both frontend and backend
npm run start:dev

# Or start separately
npm run start:server  # Backend on port 3001
npm start            # Frontend on port 3000
```

## 📁 Project Structure

```
onthemove-admin/
├── public/                 # Static files
├── src/                   # React frontend
│   ├── components/        # Reusable components
│   ├── pages/            # Page components
│   │   ├── admin/        # Admin dashboard pages
│   │   └── super-admin/  # Super admin pages
│   ├── layouts/          # Layout components
│   ├── routes/           # Route definitions
│   ├── services/         # API service functions
│   ├── store/            # Redux store configuration
│   ├── tenant/           # Multi-tenant context
│   ├── utils/            # Utility functions
│   └── App.js           # Main app component
├── server/               # Express backend
│   ├── config/          # Configuration files
│   ├── database/        # Database setup & migrations
│   ├── middleware/      # Express middleware
│   ├── routes/          # API route handlers
│   └── index.js         # Server entry point
├── package.json         # Dependencies and scripts
└── README.md           # Project documentation
```

## 🛠️ Development Commands

### Frontend Commands
```bash
npm start              # Start React development server
npm run build          # Build for production
npm test              # Run tests
npm run eject         # Eject from Create React App
```

### Backend Commands
```bash
npm run start:server   # Start Express server
npm run dev:server     # Start server with nodemon
npm run db:migrate     # Run database migrations
npm run db:seed       # Seed database with test data
npm run db:reset      # Reset database
```

### Combined Commands
```bash
npm run start:dev      # Start both frontend and backend
npm run build:all      # Build both frontend and backend
npm run test:all       # Run all tests
```

## 🏗️ Architecture Overview

### Frontend Architecture
- **React 19.1.0** - Main frontend framework
- **Redux Toolkit** - State management
- **React Router** - Client-side routing
- **Tailwind CSS** - Styling framework
- **React Hook Form** - Form management
- **Mapbox GL JS** - Interactive maps
- **Socket.IO Client** - Real-time communication

### Backend Architecture
- **Express.js** - Web framework
- **PostgreSQL** - Primary database
- **JWT** - Authentication
- **Socket.IO** - Real-time communication
- **Multer** - File upload handling
- **Helmet** - Security middleware

### Database Schema
The application uses PostgreSQL with the following main tables:
- `organizations` - Multi-tenant organization data
- `users` - User accounts and profiles
- `locations` - Work locations and sites
- `assignments` - Employee-location assignments
- `incidents` - Safety incidents and reports
- `geofences` - Geofencing boundaries
- `messages` - Communication records

## 🔧 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes
# Add tests
# Commit changes
git add .
git commit -m "Add new feature: description"

# Push to remote
git push origin feature/new-feature-name

# Create pull request
```

### 2. Code Standards
- Use **ESLint** for code linting
- Follow **Prettier** for code formatting
- Write **JSDoc** comments for functions
- Use **semantic commit messages**

### 3. Testing
```bash
# Run frontend tests
npm test

# Run backend tests
npm run test:server

# Run all tests
npm run test:all
```

## 🎨 Styling Guidelines

### Tailwind CSS
We use Tailwind CSS for styling. Key principles:
- Use utility classes for most styling
- Create custom components for complex patterns
- Follow mobile-first responsive design
- Use consistent spacing and color schemes

### Component Structure
```jsx
// Example component structure
import React from 'react';
import { classNames } from '../utils/classNames';

const ComponentName = ({ prop1, prop2, className }) => {
  return (
    <div className={classNames('base-classes', className)}>
      {/* Component content */}
    </div>
  );
};

export default ComponentName;
```

## 📱 API Development

### Creating New Endpoints
1. **Define route in server/routes/**
```javascript
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');

router.get('/', authenticateToken, async (req, res) => {
  try {
    // Implementation
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
```

2. **Add route to main server**
```javascript
// In server/index.js
const newRoutes = require('./routes/new-routes');
app.use('/api/new-endpoint', newRoutes);
```

3. **Create service function**
```javascript
// In src/services/api.js
export const newEndpointService = {
  getAll: () => api.get('/new-endpoint'),
  create: (data) => api.post('/new-endpoint', data),
  update: (id, data) => api.put(`/new-endpoint/${id}`, data),
  delete: (id) => api.delete(`/new-endpoint/${id}`)
};
```

## 🔐 Security Best Practices

### Authentication
- Use JWT tokens for stateless authentication
- Implement token refresh mechanism
- Hash passwords with bcrypt
- Validate all inputs

### Authorization
- Implement role-based access control (RBAC)
- Check permissions on both frontend and backend
- Use middleware for route protection

### Data Protection
- Sanitize user inputs
- Use parameterized queries
- Implement rate limiting
- Use HTTPS in production

## 🌐 Multi-tenant Architecture

### Tenant Context
The application uses React Context for tenant management:
```jsx
// Using tenant context
const { currentTenant, branding } = useTenant();
```

### Database Isolation
- Each organization has isolated data
- Use organization ID in all queries
- Implement proper data access controls

## 📊 State Management

### Redux Store Structure
```javascript
store/
├── index.js           # Store configuration
├── authSlice.js       # Authentication state
├── userSlice.js       # User management
├── locationSlice.js   # Location management
├── incidentSlice.js   # Incident management
└── uiSlice.js         # UI state
```

### Using Redux
```jsx
// In components
import { useSelector, useDispatch } from 'react-redux';
import { selectCurrentUser } from '../store/authSlice';

const MyComponent = () => {
  const currentUser = useSelector(selectCurrentUser);
  const dispatch = useDispatch();
  
  // Component logic
};
```

## 🧪 Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with React Testing Library
- **Integration Tests**: API integration testing
- **E2E Tests**: User workflow testing

### Backend Testing
- **Unit Tests**: Function and middleware testing
- **Integration Tests**: API endpoint testing
- **Database Tests**: Database operation testing

### Test Structure
```javascript
describe('Component/Function Name', () => {
  beforeEach(() => {
    // Setup
  });

  it('should do something', () => {
    // Test implementation
  });

  afterEach(() => {
    // Cleanup
  });
});
```

## 🚀 Deployment

### Development Deployment
```bash
# Build frontend
npm run build

# Start production server
npm run start:prod
```

### Production Deployment
1. **Environment Variables**: Set production environment variables
2. **Database**: Run migrations on production database
3. **Build**: Create production build
4. **Deploy**: Deploy to hosting platform

### Docker Support
```dockerfile
# Dockerfile example
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "run", "start:prod"]
```

## 📝 Documentation

### Code Documentation
- Document all public functions with JSDoc
- Include examples in documentation
- Keep README files updated

### API Documentation
- Document all endpoints
- Include request/response examples
- Update API documentation with changes

## 🐛 Debugging

### Development Tools
- **React Developer Tools**: Browser extension
- **Redux DevTools**: State debugging
- **VS Code Debugger**: Server-side debugging

### Common Issues
1. **CORS Issues**: Check CORS configuration
2. **Database Connection**: Verify connection string
3. **Token Expiration**: Check JWT expiration
4. **Mapbox Issues**: Verify API token

## 📞 Getting Help

### Resources
- **Project Documentation**: See README.md and setup guides
- **API Reference**: See API_DOCUMENTATION.md
- **Architecture**: See ARCHITECTURE_OVERVIEW.md

### Support
- Create GitHub issues for bugs
- Use discussions for questions
- Contact the development team for urgent issues

---

Happy coding! 🚀