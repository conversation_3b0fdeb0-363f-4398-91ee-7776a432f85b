{"name": "onthemove-admin", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.4", "@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.12.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.0", "cors": "^2.8.5", "date-fns": "^3.3.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.525.0", "mapbox-gl": "^3.13.0", "morgan": "^1.10.0", "pg": "^8.11.3", "react": "^19.1.0", "react-datepicker": "^6.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.4.1", "react-map-gl": "^7.1.9", "react-query": "^3.39.3", "react-redux": "^9.2.0", "react-router-dom": "^6.30.1", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "zod": "^3.22.4"}, "scripts": {"start": "react-scripts start", "start:server": "node server/index.js", "start:dev": "concurrently \"npm run start:server\" \"npm start\"", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(axios)/)"], "moduleNameMapper": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.17", "concurrently": "^8.2.2", "identity-obj-proxy": "^3.0.0", "nodemon": "^3.0.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1"}, "proxy": "http://localhost:3001"}