const { pool } = require('../config/database');
const fs = require('fs');
const path = require('path');

const initializeDatabase = async () => {
  try {
    console.log('Starting database initialization...');

    // Read and execute the full schema
    const schemaPath = path.join(__dirname, 'full_schema.sql');
    if (fs.existsSync(schemaPath)) {
      console.log('Executing full schema...');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Execute the full schema as a single query to support multi-line statements
      try {
        await pool.query(schema);
        console.log('Full schema executed successfully');
      } catch (error) {
        console.error('Schema execution error:', error.message);
      }
    } else {
      console.log('Full schema not found, creating basic organizations table...');
      
      // Create basic organizations table if full schema doesn't exist
      try {
        await pool.query(`
          CREATE TABLE IF NOT EXISTS organizations (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(50),
            address_line1 VARCHAR(255),
            contact_person VARCHAR(255),
            contact_email VARCHAR(255),
            contact_phone VARCHAR(50),
            subscription_plan VARCHAR(50) DEFAULT 'basic',
            billing_address_line1 VARCHAR(255),
            payment_method VARCHAR(50) DEFAULT 'invoice',
            max_users INTEGER DEFAULT 100,
            max_locations INTEGER DEFAULT 10,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        // Create indexes
        try {
          await pool.query(`
            CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);
            CREATE INDEX IF NOT EXISTS idx_organizations_email ON organizations(email);
            CREATE INDEX IF NOT EXISTS idx_organizations_status ON organizations(status);
          `);
        } catch (error) {
          console.log('Indexes may already exist:', error.message);
        }
        
        console.log('Basic organizations table created successfully');
      } catch (error) {
        console.log('Organizations table may already exist:', error.message);
      }
    }

    // Check if we need to insert sample data
    try {
      const orgCount = await pool.query('SELECT COUNT(*) FROM organizations');
      if (parseInt(orgCount.rows[0].count) === 0) {
        console.log('Inserting sample organizations...');
        
        // Insert sample organizations
        await pool.query(`
          INSERT INTO organizations (id, name, email, contact_person, subscription_plan, max_users, max_locations, status) VALUES
          ('00000000-0000-0000-0000-000000000001', 'Acme Security', '<EMAIL>', 'Alice Manager', 'premium', 100, 10, 'active'),
          ('00000000-0000-0000-0000-000000000002', 'City Guard', '<EMAIL>', 'Bob Supervisor', 'standard', 50, 5, 'active'),
          ('00000000-0000-0000-0000-000000000003', 'Metro Protection', '<EMAIL>', 'Carol Admin', 'enterprise', 200, 20, 'active');
        `);
        
        console.log('Sample organizations inserted successfully');
      } else {
        console.log(`Database already has ${orgCount.rows[0].count} organizations`);
      }
    } catch (error) {
      console.log('Error checking/inserting sample data:', error.message);
    }

    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

module.exports = { initializeDatabase }; 