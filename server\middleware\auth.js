const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Generate JWT token
const generateToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

// Verify JWT token
const verifyToken = (token) => {
  return jwt.verify(token, JWT_SECRET);
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const decoded = verifyToken(token);
    
    // Get user from database to ensure they still exist and are active
    const result = await pool.query(
      'SELECT id, email, first_name, last_name, role, status, organization_id FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ error: 'User not found' });
    }

    const user = result.rows[0];
    
    if (user.status !== 'active') {
      return res.status(401).json({ error: 'User account is not active' });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Authentication error' });
  }
};

// Role authorization middleware
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
};

// Tenant authorization middleware
const requireTenantAccess = async (req, res, next) => {
  try {
    const tenantId = req.params.tenantId;
    const user = req.user;

    // Super admins can access any tenant
    if (user.role === 'super_admin') {
      return next();
    }

    // Regular users can only access their own tenant
    if (!user.tenant_id || user.tenant_id !== tenantId) {
      return res.status(403).json({ error: 'Access denied to this tenant' });
    }

    next();
  } catch (error) {
    console.error('Tenant authorization error:', error);
    res.status(500).json({ error: 'Authorization error' });
  }
};

module.exports = {
  generateToken,
  verifyToken,
  authenticateToken,
  requireRole,
  requireTenantAccess
}; 