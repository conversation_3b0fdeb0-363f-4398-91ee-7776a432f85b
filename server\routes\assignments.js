const express = require('express');
const { authenticateToken, requireTenantAccess } = require('../middleware/auth');
const { pool } = require('../config/database');
const router = express.Router({ mergeParams: true });

// Get all assignments for a tenant
router.get('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      search = '', 
      userId = 'all', 
      locationId = 'all',
      status = 'all',
      dateFrom,
      dateTo,
      page = 1, 
      limit = 50 
    } = req.query;

    let query = `
      SELECT 
        a.id,
        a.shift_start,
        a.shift_end,
        a.status,
        a.notes,
        a.created_at,
        a.updated_at,
        u.id as user_id,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        l.id as location_id,
        l.name as location_name,
        l.address as location_address,
        l.type as location_type
      FROM assignments a
      JOIN users u ON a.user_id = u.id
      JOIN locations l ON a.location_id = l.id
      WHERE a.organization_id = $1
    `;
    
    const params = [organizationId];
    let paramIndex = 2;

    // Apply filters
    if (search) {
      query += ` AND (u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR l.name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (userId !== 'all') {
      query += ` AND a.user_id = $${paramIndex}`;
      params.push(userId);
      paramIndex++;
    }

    if (locationId !== 'all') {
      query += ` AND a.location_id = $${paramIndex}`;
      params.push(locationId);
      paramIndex++;
    }

    if (status !== 'all') {
      query += ` AND a.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (dateFrom) {
      query += ` AND a.shift_start >= $${paramIndex}`;
      params.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      query += ` AND a.shift_end <= $${paramIndex}`;
      params.push(dateTo);
      paramIndex++;
    }

    query += ` 
      ORDER BY a.shift_start DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, (page - 1) * limit);

    const result = await pool.query(query, params);
    const assignments = result.rows.map(assignment => ({
      id: assignment.id,
      shiftStart: assignment.shift_start,
      shiftEnd: assignment.shift_end,
      status: assignment.status,
      notes: assignment.notes,
      createdAt: assignment.created_at,
      updatedAt: assignment.updated_at,
      employee: {
        id: assignment.user_id,
        name: `${assignment.first_name} ${assignment.last_name}`,
        email: assignment.email,
        role: assignment.role
      },
      location: {
        id: assignment.location_id,
        name: assignment.location_name,
        address: assignment.location_address,
        type: assignment.location_type
      }
    }));

    res.json(assignments);
  } catch (error) {
    console.error('Error fetching assignments:', error);
    res.status(500).json({ error: 'Failed to fetch assignments' });
  }
});

// Get assignment by ID
router.get('/:assignmentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, assignmentId } = req.params;

    const result = await pool.query(`
      SELECT 
        a.id,
        a.shift_start,
        a.shift_end,
        a.status,
        a.notes,
        a.created_at,
        a.updated_at,
        u.id as user_id,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        l.id as location_id,
        l.name as location_name,
        l.address as location_address,
        l.type as location_type
      FROM assignments a
      JOIN users u ON a.user_id = u.id
      JOIN locations l ON a.location_id = l.id
      WHERE a.organization_id = $1 AND a.id = $2
    `, [organizationId, assignmentId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    const assignment = result.rows[0];

    res.json({
      id: assignment.id,
      shiftStart: assignment.shift_start,
      shiftEnd: assignment.shift_end,
      status: assignment.status,
      notes: assignment.notes,
      createdAt: assignment.created_at,
      updatedAt: assignment.updated_at,
      employee: {
        id: assignment.user_id,
        name: `${assignment.first_name} ${assignment.last_name}`,
        email: assignment.email,
        role: assignment.role
      },
      location: {
        id: assignment.location_id,
        name: assignment.location_name,
        address: assignment.location_address,
        type: assignment.location_type
      }
    });
  } catch (error) {
    console.error('Error fetching assignment:', error);
    res.status(500).json({ error: 'Failed to fetch assignment' });
  }
});

// Create new assignment
router.post('/', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { 
      userId, 
      locationId, 
      shiftStart, 
      shiftEnd, 
      notes = '',
      recurring = false,
      recurringDays = []
    } = req.body;

    // Validate required fields
    if (!userId || !locationId || !shiftStart || !shiftEnd) {
      return res.status(400).json({ error: 'User ID, location ID, shift start, and shift end are required' });
    }

    // Check if user and location belong to tenant
    const userCheck = await pool.query(`
      SELECT id FROM users WHERE id = $1 AND organization_id = $2
    `, [userId, organizationId]);

    const locationCheck = await pool.query(`
      SELECT id FROM locations WHERE id = $1 AND organization_id = $2
    `, [locationId, organizationId]);

    if (userCheck.rows.length === 0) {
      return res.status(400).json({ error: 'User not found or does not belong to tenant' });
    }

    if (locationCheck.rows.length === 0) {
      return res.status(400).json({ error: 'Location not found or does not belong to tenant' });
    }

    // Check for overlapping assignments
    const overlapCheck = await pool.query(`
      SELECT id FROM assignments 
      WHERE user_id = $1 AND status = 'active'
        AND (
          (shift_start <= $2 AND shift_end > $2) OR
          (shift_start < $3 AND shift_end >= $3) OR
          (shift_start >= $2 AND shift_end <= $3)
        )
    `, [userId, shiftStart, shiftEnd]);

    if (overlapCheck.rows.length > 0) {
      return res.status(409).json({ error: 'User already has an overlapping assignment' });
    }

    // Create assignment
    const result = await pool.query(`
      INSERT INTO assignments (
        organization_id, user_id, location_id, shift_start, shift_end, status, notes
      ) VALUES ($1, $2, $3, $4, $5, 'active', $6)
      RETURNING id, shift_start, shift_end, status, notes, created_at
    `, [organizationId, userId, locationId, shiftStart, shiftEnd, notes]);

    const newAssignment = result.rows[0];

    // Get the full assignment details
    const fullAssignment = await pool.query(`
      SELECT 
        a.id,
        a.shift_start,
        a.shift_end,
        a.status,
        a.notes,
        a.created_at,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        l.name as location_name,
        l.address as location_address,
        l.type as location_type
      FROM assignments a
      JOIN users u ON a.user_id = u.id
      JOIN locations l ON a.location_id = l.id
      WHERE a.id = $1
    `, [newAssignment.id]);

    const assignment = fullAssignment.rows[0];

    res.status(201).json({
      id: assignment.id,
      shiftStart: assignment.shift_start,
      shiftEnd: assignment.shift_end,
      status: assignment.status,
      notes: assignment.notes,
      createdAt: assignment.created_at,
      employee: {
        name: `${assignment.first_name} ${assignment.last_name}`,
        email: assignment.email,
        role: assignment.role
      },
      location: {
        name: assignment.location_name,
        address: assignment.location_address,
        type: assignment.location_type
      }
    });
  } catch (error) {
    console.error('Error creating assignment:', error);
    res.status(500).json({ error: 'Failed to create assignment' });
  }
});

// Update assignment
router.put('/:assignmentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, assignmentId } = req.params;
    const { userId, locationId, shiftStart, shiftEnd, status, notes } = req.body;

    // Check if assignment exists and belongs to tenant
    const existingAssignment = await pool.query(`
      SELECT id, user_id FROM assignments WHERE id = $1 AND organization_id = $2
    `, [assignmentId, organizationId]);

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    // Check for overlapping assignments if changing time or user
    if ((userId && userId !== existingAssignment.rows[0].user_id) || shiftStart || shiftEnd) {
      const checkUserId = userId || existingAssignment.rows[0].user_id;
      const overlapCheck = await pool.query(`
        SELECT id FROM assignments 
        WHERE user_id = $1 AND status = 'active' AND id != $2
          AND (
            (shift_start <= $3 AND shift_end > $3) OR
            (shift_start < $4 AND shift_end >= $4) OR
            (shift_start >= $3 AND shift_end <= $4)
          )
      `, [checkUserId, assignmentId, shiftStart, shiftEnd]);

      if (overlapCheck.rows.length > 0) {
        return res.status(409).json({ error: 'User already has an overlapping assignment' });
      }
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (userId) {
      updates.push(`user_id = $${paramIndex}`);
      params.push(userId);
      paramIndex++;
    }
    if (locationId) {
      updates.push(`location_id = $${paramIndex}`);
      params.push(locationId);
      paramIndex++;
    }
    if (shiftStart) {
      updates.push(`shift_start = $${paramIndex}`);
      params.push(shiftStart);
      paramIndex++;
    }
    if (shiftEnd) {
      updates.push(`shift_end = $${paramIndex}`);
      params.push(shiftEnd);
      paramIndex++;
    }
    if (status) {
      updates.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }
    if (notes !== undefined) {
      updates.push(`notes = $${paramIndex}`);
      params.push(notes);
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = NOW()`);
    params.push(assignmentId, organizationId);

    const result = await pool.query(`
      UPDATE assignments 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND organization_id = $${paramIndex + 1}
      RETURNING id, shift_start, shift_end, status, notes, updated_at
    `, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating assignment:', error);
    res.status(500).json({ error: 'Failed to update assignment' });
  }
});

// Delete assignment
router.delete('/:assignmentId', [authenticateToken, requireTenantAccess], async (req, res) => {
  try {
    const { organizationId, assignmentId } = req.params;

    // Set assignment to cancelled instead of deleting
    await pool.query(`
      UPDATE assignments 
      SET status = 'cancelled', updated_at = NOW()
      WHERE id = $1 AND organization_id = $2
    `, [assignmentId, organizationId]);

    res.json({ message: 'Assignment cancelled successfully' });
  } catch (error) {
    console.error('Error deleting assignment:', error);
    res.status(500).json({ error: 'Failed to delete assignment' });
  }
});

module.exports = router; 