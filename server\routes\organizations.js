const express = require('express');
const { body, validationResult } = require('express-validator');
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

const router = express.Router();

// Test endpoint (no auth required)
router.get('/test', async (req, res) => {
  try {
    res.json({ 
      message: 'Organizations API is working!',
      timestamp: new Date().toISOString(),
      database: 'connected'
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all organizations (Super Admin only)
router.get('/', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      params.push(status);
    }

    // Get total count
    const countResult = await pool.query(
      `SELECT COUNT(*) FROM organizations ${whereClause}`,
      params
    );
    const total = parseInt(countResult.rows[0].count);

    // Get organizations with pagination and user counts
    params.push(limit, offset);
    const result = await pool.query(
      `SELECT o.id, o.name, o.email, o.phone, o.address_line1 as address, o.contact_person, o.contact_email,
              o.subscription_plan, o.status, o.created_at, o.updated_at, o.max_users, o.max_locations,
              COALESCE(u.user_count, 0) as user_count
       FROM organizations o
       LEFT JOIN (
         SELECT organization_id, COUNT(*) as user_count
         FROM users
         WHERE status = 'active'
         GROUP BY organization_id
       ) u ON o.id = u.organization_id
       ${whereClause}
       ORDER BY o.created_at DESC
       LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
      params
    );

    res.json({
      organizations: result.rows, // Keep as tenants for frontend compatibility
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get organizations error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get organization by ID
router.get('/:id', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      `SELECT * FROM organizations WHERE id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Get organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new organization
router.post('/', [
  authenticateToken,
  requireRole(['super_admin']),
  body('name').isLength({ min: 1 }).trim(),
  body('email').isEmail().normalizeEmail(),
  body('phone').optional().isMobilePhone(),
  body('address').optional().isLength({ min: 1 }).trim(),
  body('contactPerson').isLength({ min: 1 }).trim(),
  body('contactEmail').isEmail().normalizeEmail(),
  body('subscriptionPlan').optional().isIn(['basic', 'standard', 'premium', 'enterprise'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      name, email, phone, address, contactPerson, contactEmail,
      contactPhone, subscriptionPlan, billingAddress, paymentMethod,
      maxUsers, maxLocations, status
    } = req.body;

    const result = await pool.query(
      `INSERT INTO organizations (
        name, email, phone, address_line1, contact_person, contact_email,
        contact_phone, subscription_plan, billing_address_line1, payment_method,
        max_users, max_locations, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *`,
      [
        name, email, phone, address, contactPerson, contactEmail,
        contactPhone, subscriptionPlan || 'basic', billingAddress,
        paymentMethod || 'invoice', maxUsers || 100, maxLocations || 10,
        status || 'active'
      ]
    );

    res.status(201).json(result.rows[0]);

  } catch (error) {
    console.error('Create organization error:', error);
    if (error.code === '23505') { // Unique constraint violation
      res.status(409).json({ error: 'Email already exists' });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

// Update organization
router.put('/:id', [
  authenticateToken,
  requireRole(['super_admin']),
  body('name').optional().isLength({ min: 1 }).trim(),
  body('email').optional().isEmail().normalizeEmail(),
  body('phone').optional().isLength({ min: 0 }).trim(),
  body('contactPhone').optional().isLength({ min: 0 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.error('Validation errors:', errors.array());
      console.error('Request body:', req.body);
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const updates = req.body;

    // Map frontend field names to database field names
    const fieldMapping = {
      name: 'name',
      email: 'email',
      phone: 'phone',
      address: 'address_line1',
      contactPerson: 'contact_person',
      contactEmail: 'contact_email',
      contactPhone: 'contact_phone',
      subscriptionPlan: 'subscription_plan',
      billingAddress: 'billing_address_line1',
      paymentMethod: 'payment_method',
      maxUsers: 'max_users',
      maxLocations: 'max_locations',
      status: 'status'
    };

    // Build dynamic query
    const setClause = [];
    const params = [];
    let paramCount = 0;

    Object.keys(updates).forEach(key => {
      if (updates[key] !== undefined && fieldMapping[key]) {
        paramCount++;
        setClause.push(`${fieldMapping[key]} = $${paramCount}`);
        params.push(updates[key]);
      }
    });

    if (setClause.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    params.push(id);
    const result = await pool.query(
      `UPDATE organizations SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP
       WHERE id = $${paramCount + 1}
       RETURNING *`,
      params
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(result.rows[0]);

  } catch (error) {
    console.error('Update organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete organization
router.delete('/:id', [authenticateToken, requireRole(['super_admin'])], async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      `DELETE FROM organizations WHERE id = $1 RETURNING id`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json({ message: 'Organization deleted successfully' });

  } catch (error) {
    console.error('Delete organization error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router; 