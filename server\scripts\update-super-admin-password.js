const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function updateSuperAdminPassword() {
  try {
    console.log('Updating super admin password...');
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Update super admin password
    const result = await pool.query(`
      UPDATE users 
      SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
      WHERE email = $2 AND role = $3
      RETURNING id, email, role
    `, [
      hashedPassword,
      '<EMAIL>',
      'super_admin'
    ]);
    
    if (result.rows.length > 0) {
      console.log('Super admin password updated successfully:', result.rows[0]);
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
    } else {
      console.log('Super admin user not found');
    }
    
  } catch (error) {
    console.error('Error updating super admin password:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
updateSuperAdminPassword();
