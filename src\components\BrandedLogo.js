import React from 'react';

const BrandedLogo = ({ 
  size = 'md', 
  showText = true, 
  className = '',
  companyName = 'OnTheMove',
  logoUrl = null,
  primaryColor = '#3b82f6'
}) => {
  const name = companyName;
  
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8', 
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };
  
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {logoUrl ? (
        <img 
          src={logoUrl} 
          alt={`${name} Logo`} 
          className={`${sizeClasses[size]} w-auto`}
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }}
        />
      ) : null}
      <div 
        className={`${sizeClasses[size]} rounded-full flex items-center justify-center text-white font-bold ${!logoUrl ? 'flex' : 'hidden'}`}
        style={{ backgroundColor: primaryColor }}
      >
        {name.charAt(0).toUpperCase()}
      </div>
      {showText && (
        <span 
          className={`font-bold ${textSizeClasses[size]}`}
          style={{ color: primaryColor }}
        >
          {name}
        </span>
      )}
    </div>
  );
};

export default BrandedLogo; 