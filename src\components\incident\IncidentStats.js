import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>xis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { AlertTriangle, TrendingUp, TrendingDown, Clock, CheckCircle } from 'lucide-react';

const IncidentStats = ({ incidents, employees, locations }) => {
  // Calculate statistics
  const getStats = () => {
    const stats = {
      total: incidents.length,
      open: incidents.filter(inc => inc.status === 'open').length,
      inProgress: incidents.filter(inc => inc.status === 'in_progress').length,
      resolved: incidents.filter(inc => inc.status === 'resolved').length,
      closed: incidents.filter(inc => inc.status === 'closed').length,
      critical: incidents.filter(inc => inc.severity === 'critical').length,
      high: incidents.filter(inc => inc.severity === 'high').length,
      medium: incidents.filter(inc => inc.severity === 'medium').length,
      low: incidents.filter(inc => inc.severity === 'low').length
    };
    return stats;
  };

  // Get status distribution data for pie chart
  const getStatusDistribution = () => {
    const stats = getStats();
    return [
      { name: 'Open', value: stats.open, color: '#ef4444' },
      { name: 'In Progress', value: stats.inProgress, color: '#f59e0b' },
      { name: 'Resolved', value: stats.resolved, color: '#10b981' },
      { name: 'Closed', value: stats.closed, color: '#6b7280' }
    ].filter(item => item.value > 0);
  };

  // Get severity distribution data for bar chart
  const getSeverityDistribution = () => {
    const stats = getStats();
    return [
      { name: 'Low', value: stats.low, color: '#10b981' },
      { name: 'Medium', value: stats.medium, color: '#f59e0b' },
      { name: 'High', value: stats.high, color: '#f97316' },
      { name: 'Critical', value: stats.critical, color: '#ef4444' }
    ];
  };

  // Get incidents by location
  const getIncidentsByLocation = () => {
    const locationMap = {};
    incidents.forEach(incident => {
      const location = locations.find(loc => loc.id === incident.locationId);
      const locationName = location ? location.name : 'Unknown';
      locationMap[locationName] = (locationMap[locationName] || 0) + 1;
    });
    
    return Object.entries(locationMap).map(([name, value]) => ({
      name,
      value,
      color: '#3b82f6'
    }));
  };

  // Get incidents by employee
  const getIncidentsByEmployee = () => {
    const employeeMap = {};
    incidents.forEach(incident => {
      if (incident.assignedEmployeeId) {
        const employee = employees.find(emp => emp.id === incident.assignedEmployeeId);
        const employeeName = employee ? employee.name : 'Unknown';
        employeeMap[employeeName] = (employeeMap[employeeName] || 0) + 1;
      }
    });
    
    return Object.entries(employeeMap)
      .map(([name, value]) => ({ name, value, color: '#8b5cf6' }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 employees
  };

  // Get incidents by category
  const getIncidentsByCategory = () => {
    const categoryMap = {};
    incidents.forEach(incident => {
      const category = incident.category || 'general';
      categoryMap[category] = (categoryMap[category] || 0) + 1;
    });
    
    return Object.entries(categoryMap).map(([name, value]) => ({
      name: name.charAt(0).toUpperCase() + name.slice(1),
      value,
      color: '#06b6d4'
    }));
  };

  // Get monthly trend data
  const getMonthlyTrend = () => {
    const monthMap = {};
    const now = new Date();
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, 1);
    
    incidents.forEach(incident => {
      const incidentDate = new Date(incident.submittedAt);
      if (incidentDate >= sixMonthsAgo) {
        const monthKey = incidentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        monthMap[monthKey] = (monthMap[monthKey] || 0) + 1;
      }
    });
    
    return Object.entries(monthMap)
      .map(([name, value]) => ({ name, incidents: value }))
      .sort((a, b) => new Date(a.name) - new Date(b.name));
  };

  const stats = getStats();
  const statusDistribution = getStatusDistribution();
  const severityDistribution = getSeverityDistribution();
  const locationData = getIncidentsByLocation();
  const employeeData = getIncidentsByEmployee();
  const categoryData = getIncidentsByCategory();
  const monthlyTrend = getMonthlyTrend();

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="p-6 space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Incidents</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-gray-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Open</p>
              <p className="text-2xl font-bold text-red-600">{stats.open}</p>
            </div>
            <Clock className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-green-600">{stats.resolved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Status Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={statusDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Severity Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Severity Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={severityDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Monthly Trend */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Trend (Last 6 Months)</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="incidents" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Incidents by Category */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Incidents by Category</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={categoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#06b6d4" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Location and Employee Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Incidents by Location */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Incidents by Location</h3>
          <div className="space-y-3">
            {locationData.map((location, index) => (
              <div key={location.name} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{location.name}</span>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${(location.value / Math.max(...locationData.map(l => l.value))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{location.value}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Employees by Incidents */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Employees by Incidents</h3>
          <div className="space-y-3">
            {employeeData.map((employee, index) => (
              <div key={employee.name} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{employee.name}</span>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{
                        width: `${(employee.value / Math.max(...employeeData.map(e => e.value))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{employee.value}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Key Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {stats.total > 0 ? ((stats.resolved / stats.total) * 100).toFixed(1) : 0}%
            </p>
            <p className="text-sm text-gray-600">Resolution Rate</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {stats.total > 0 ? ((stats.critical + stats.high) / stats.total * 100).toFixed(1) : 0}%
            </p>
            <p className="text-sm text-gray-600">High/Critical Rate</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {stats.open > 0 ? (stats.open / stats.total * 100).toFixed(1) : 0}%
            </p>
            <p className="text-sm text-gray-600">Open Rate</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncidentStats; 