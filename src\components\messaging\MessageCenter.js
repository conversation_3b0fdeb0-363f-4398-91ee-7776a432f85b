import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import MessageList from './MessageList';
import MessageThread from './MessageThread';
import MessageComposer from './MessageComposer';
import AlertBanner from './AlertBanner';
import NotificationCenter from './NotificationCenter';
import { 
  MessageSquare, 
  Users, 
  Bell, 
  X, 
  Minimize2,
  Maximize2,
  Send,
  AlertTriangle,
  Shield,
  MapPin
} from 'lucide-react';
import BrandedButton from '../BrandedButton';
import {
  fetchConversations,
  fetchMessages,
  sendMessage,
  markAsRead,
  selectConversations,
  selectCurrentConversation,
  selectMessages,
  selectMessagingLoading,
  selectMessagingError
} from '../../store/slices/messagingSlice';
import {
  selectNotifications,
  markNotificationAsRead,
  clearAllNotifications
} from '../../store/slices/notificationSlice';

const MessageCenter = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  // Redux state
  const conversations = useSelector(selectConversations);
  const currentConversation = useSelector(selectCurrentConversation);
  const messages = useSelector(selectMessages);
  const loading = useSelector(selectMessagingLoading);
  const error = useSelector(selectMessagingError);
  const notifications = useSelector(selectNotifications);

  // Local state
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [selectedConversationId, setSelectedConversationId] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState({});
  const [unreadCount, setUnreadCount] = useState(0);
  const [alertBanners, setAlertBanners] = useState([]);

  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Load conversations on mount
  useEffect(() => {
    if (tenant?.id && isOpen) {
      dispatch(fetchConversations({ tenantId: tenant.id }));
    }
  }, [dispatch, tenant?.id, isOpen]);

  // Load messages when conversation is selected
  useEffect(() => {
    if (selectedConversationId && tenant?.id) {
      dispatch(fetchMessages({ 
        tenantId: tenant.id, 
        conversationId: selectedConversationId 
      }));
    }
  }, [dispatch, tenant?.id, selectedConversationId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Calculate unread count
  useEffect(() => {
    const count = conversations.reduce((total, conv) => {
      return total + (conv.unreadCount || 0);
    }, 0);
    setUnreadCount(count);
  }, [conversations]);

  // Handle new message submission
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversationId) return;

    try {
      await dispatch(sendMessage({
        tenantId: tenant.id,
        conversationId: selectedConversationId,
        content: newMessage.trim(),
        senderId: user.id
      }));
      setNewMessage('');
      setIsTyping(false);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle typing indicator
  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      // Emit typing event via WebSocket
      // webSocketService.emitTyping(selectedConversationId, user.id);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 3000);
  };

  // Handle conversation selection
  const handleConversationSelect = (conversationId) => {
    setSelectedConversationId(conversationId);
    // Mark conversation as read
    dispatch(markAsRead({ 
      tenantId: tenant.id, 
      conversationId 
    }));
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    // Mark notification as read
    dispatch(markNotificationAsRead(notification.id));
    
    // Handle different notification types
    switch (notification.type) {
      case 'incident':
        // Navigate to incident management
        window.location.href = `/org/${tenant.id}/incidents`;
        break;
      case 'panic_button':
        // Navigate to live map
        window.location.href = `/org/${tenant.id}/live-map`;
        break;
      case 'geofence_breach':
        // Navigate to live map
        window.location.href = `/org/${tenant.id}/live-map`;
        break;
      case 'message':
        // Open message center
        setIsOpen(true);
        if (notification.conversationId) {
          setSelectedConversationId(notification.conversationId);
        }
        break;
      default:
        break;
    }
  };

  // Add alert banner
  const addAlertBanner = (alert) => {
    const id = Date.now();
    setAlertBanners(prev => [...prev, { ...alert, id }]);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      removeAlertBanner(id);
    }, 10000);
  };

  // Remove alert banner
  const removeAlertBanner = (id) => {
    setAlertBanners(prev => prev.filter(alert => alert.id !== id));
  };

  // Handle key press for sending message
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!tenant?.id) {
    return null;
  }

  return (
    <>
      {/* Alert Banners */}
      {alertBanners.map((alert) => (
        <AlertBanner
          key={alert.id}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          onClose={() => removeAlertBanner(alert.id)}
          action={alert.action}
        />
      ))}

      {/* Floating Action Button */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <div className="flex flex-col gap-2">
            {/* Message Center Button */}
            <button
              onClick={() => setIsOpen(true)}
              className="relative p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
              style={{ backgroundColor: branding?.primaryColor || '#3b82f6' }}
            >
              <MessageSquare className="h-6 w-6 text-white" />
              {unreadCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </button>

            {/* Notifications Button */}
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-white border border-gray-200"
            >
              <Bell className="h-6 w-6 text-gray-600" />
              {notifications.filter(n => !n.read).length > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                  {notifications.filter(n => !n.read).length}
                </span>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Notification Center */}
      {showNotifications && !isOpen && (
        <div className="fixed bottom-20 right-6 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-40">
          <NotificationCenter
            notifications={notifications}
            onNotificationClick={handleNotificationClick}
            onClose={() => setShowNotifications(false)}
          />
        </div>
      )}

      {/* Message Center Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-end z-50">
          <div className={`bg-white rounded-t-lg shadow-2xl transition-all duration-300 ${
            isMinimized ? 'h-16' : 'h-[600px] w-96'
          }`}>
            {/* Header */}
            <div 
              className="flex items-center justify-between p-4 border-b cursor-pointer"
              style={{ borderColor: branding?.primaryColor ? `${branding.primaryColor}20` : undefined }}
              onClick={() => setIsMinimized(!isMinimized)}
            >
              <div className="flex items-center gap-2">
                <MessageSquare 
                  className="h-5 w-5" 
                  style={{ color: branding?.primaryColor || '#3b82f6' }}
                />
                <span className="font-semibold text-gray-900">Messages</span>
                {unreadCount > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {unreadCount}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsMinimized(!isMinimized);
                  }}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <div className="flex flex-col h-full">
                {/* Content */}
                <div className="flex-1 flex">
                  {/* Conversation List */}
                  <div className="w-1/3 border-r border-gray-200">
                    <MessageList
                      conversations={conversations}
                      selectedConversationId={selectedConversationId}
                      onConversationSelect={handleConversationSelect}
                      loading={loading}
                    />
                  </div>

                  {/* Message Thread */}
                  <div className="flex-1 flex flex-col">
                    {selectedConversationId ? (
                      <>
                        <MessageThread
                          messages={messages}
                          conversation={currentConversation}
                          typingUsers={typingUsers}
                          loading={loading}
                          messagesEndRef={messagesEndRef}
                        />
                        <MessageComposer
                          value={newMessage}
                          onChange={setNewMessage}
                          onSend={handleSendMessage}
                          onTyping={handleTyping}
                          onKeyPress={handleKeyPress}
                          disabled={loading}
                        />
                      </>
                    ) : (
                      <div className="flex-1 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                          <MessageSquare className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                          <p>Select a conversation to start messaging</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default MessageCenter; 