@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }

  /* CSS Variables for dynamic branding */
  :root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --login-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* Hide webpack dev server error overlay for ResizeObserver errors */
  iframe[id*="webpack"] {
    display: none !important;
  }

  #webpack-dev-server-client-overlay {
    display: none !important;
  }

  #webpack-dev-server-client-overlay-div {
    display: none !important;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn text-white hover:bg-primary-700 focus:ring-primary-500;
    background-color: var(--primary-600);
  }
  
  .btn-secondary {
    @apply btn bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(var(--primary-500), 0.1);
  }
  
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }
  
  .card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-4 py-5 sm:p-6;
  }
  
  .sidebar-item {
    @apply flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .sidebar-item-active {
    @apply sidebar-item text-primary-900;
    background-color: var(--primary-100);
  }
  
  .sidebar-item-inactive {
    @apply sidebar-item text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }

  /* Login page with dynamic background */
  .login-container {
    background: var(--login-background);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Branded text colors */
  .text-primary {
    color: var(--primary-600);
  }

  .text-primary-light {
    color: var(--primary-500);
  }

  .text-primary-dark {
    color: var(--primary-700);
  }

  /* Branded background colors */
  .bg-primary {
    background-color: var(--primary-500);
  }

  .bg-primary-light {
    background-color: var(--primary-100);
  }

  .bg-primary-dark {
    background-color: var(--primary-700);
  }

  /* Error handling styles */
  .error-suppressed {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }
}
