import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import BrandedLogo from '../components/BrandedLogo';
import MessageCenter from '../components/messaging/MessageCenter';
import NotificationCenter from '../components/messaging/NotificationCenter';
import { 
  Menu, 
  X, 
  Home, 
  Users, 
  MapPin, 
  Calendar, 
  Map, 
  AlertTriangle, 
  BarChart3,
  Shield,
  MessageCircle,
  Bell,
  Settings,
  LogOut,
  Building2,
  CreditCard
} from 'lucide-react';
import { logout } from '../store/slices/authSlice';

// Default values for tenant when context is not available
const defaultTenant = { name: 'OnTheMove Admin' };

const MainLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  // Use default values instead of context hooks
  const tenant = defaultTenant;
  
  const { user } = useSelector((state) => state.auth);
  const { unreadCount } = useSelector((state) => state.messaging);
  const { notifications } = useSelector((state) => state.notifications);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showMessageCenter, setShowMessageCenter] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  // Navigation items based on user role
  const getNavigationItems = () => {
    if (user?.role === 'super_admin') {
      // Super Admin navigation
      return [
        { name: 'Dashboard', href: '/super-admin/dashboard', icon: Home },
        { name: 'Organizations', href: '/super-admin/organizations', icon: Building2 },
        { name: 'Users', href: '/super-admin/users', icon: Users },
        { name: 'Billing', href: '/super-admin/billing', icon: CreditCard },
        { name: 'System Settings', href: '/super-admin/settings', icon: Settings },
      ];
    } else {
      // Admin navigation
      const baseItems = [
        { name: 'Dashboard', href: '/admin/dashboard', icon: Home, roles: ['client_admin', 'dispatcher', 'supervisor'] },
        { name: 'Branding', href: '/admin/branding', icon: Settings, roles: ['client_admin'] },
        { name: 'Employees', href: '/admin/employees', icon: Users, roles: ['client_admin', 'dispatcher'] },
        { name: 'Locations', href: '/admin/locations', icon: MapPin, roles: ['client_admin', 'dispatcher'] },
        { name: 'Assignments', href: '/admin/assignments', icon: Calendar, roles: ['client_admin', 'dispatcher', 'supervisor'] },
        { name: 'Live Map', href: '/admin/live-map', icon: Map, roles: ['client_admin', 'dispatcher', 'supervisor'] },
        { name: 'Incidents', href: '/admin/incidents', icon: AlertTriangle, roles: ['client_admin', 'dispatcher', 'supervisor'] },
        { name: 'Reports', href: '/admin/reports', icon: BarChart3, roles: ['client_admin', 'dispatcher'] },
        { name: 'Geofencing', href: '/admin/geofencing', icon: Shield, roles: ['client_admin', 'dispatcher'] },
      ];
      return baseItems.filter(item => item.roles.includes(user?.role));
    }
  };

  const navigationItems = getNavigationItems();

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  const isActiveRoute = (href) => {
    return location.pathname === href;
  };

  const unreadNotifications = notifications.filter(n => !n.read).length;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <BrandedLogo />
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.href);
                    setSidebarOpen(false);
                  }}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActiveRoute(item.href)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </a>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white shadow-xl">
          <div className="flex h-16 items-center px-4 border-b border-gray-200">
            <BrandedLogo />
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.href);
                  }}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActiveRoute(item.href)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </a>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1 items-center">
              <h1 className="text-lg font-semibold text-gray-900">
                {user?.role === 'super_admin' ? 'Super Admin Portal' : (tenant?.name || 'OnTheMove Admin')}
              </h1>
            </div>

            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Message Center */}
              <button
                onClick={() => setShowMessageCenter(!showMessageCenter)}
                className="relative p-2 text-gray-400 hover:text-gray-500"
              >
                <MessageCircle className="h-6 w-6" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>
                )}
              </button>

              {/* Notifications */}
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative p-2 text-gray-400 hover:text-gray-500"
              >
                <Bell className="h-6 w-6" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                    {unreadNotifications > 99 ? '99+' : unreadNotifications}
                  </span>
                )}
              </button>

              {/* User menu */}
              <div className="relative">
                <div className="flex items-center gap-x-3">
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{user?.name}</p>
                    <p className="text-gray-500 capitalize">{user?.role?.replace('_', ' ')}</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-700">
                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="p-2 text-gray-400 hover:text-gray-500"
                    title="Logout"
                  >
                    <LogOut className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content area */}
        <main className="flex-1">
          <div className="p-4 sm:p-6 lg:p-8">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Message Center Overlay */}
      {showMessageCenter && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowMessageCenter(false)} />
          <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
            <MessageCenter onClose={() => setShowMessageCenter(false)} />
          </div>
        </div>
      )}

      {/* Notifications Overlay */}
      {showNotifications && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowNotifications(false)} />
          <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
            <NotificationCenter onClose={() => setShowNotifications(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default MainLayout; 