import React from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import { login, clearError } from '../store/slices/authSlice';
import { renderErrorMessage } from '../utils/errorUtils';

const SuperAdminLogin = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading, error, isAuthenticated, user } = useSelector((state) => state.auth);
  const { register, handleSubmit, formState: { errors }, setValue } = useForm();

  const onSubmit = (data) => {
    dispatch(login({ ...data, loginType: 'super_admin' }));
  };

  const fillDemoCredentials = () => {
    setValue('email', '<EMAIL>');
    setValue('password', 'admin123');
  };

  React.useEffect(() => {
    if (isAuthenticated && user) {
      navigate('/super-admin/dashboard');
    }
  }, [isAuthenticated, user, navigate]);

  React.useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  return (
    <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-xl">
      <div className="mb-4">
        <Link
          to="/"
          className="text-sm text-gray-600 hover:text-gray-900 flex items-center"
        >
          ← Back to Home
        </Link>
      </div>

      <div className="text-center mb-6">
        <div className="h-12 w-12 mx-auto mb-4 rounded-full flex items-center justify-center text-white font-bold text-lg bg-purple-600">
          S
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Super Admin Login</h2>
        <p className="text-gray-600 mt-2">Access your super admin dashboard</p>
      </div>

      <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
        <h3 className="text-sm font-semibold text-purple-900 mb-2">🚀 Try Demo</h3>
        <div className="text-xs text-purple-700 space-y-1">
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Password:</strong> admin123</p>
        </div>
        <button
          type="button"
          onClick={fillDemoCredentials}
          className="mt-2 text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700 transition-colors"
        >
          Fill Demo Credentials
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm text-center">
            {renderErrorMessage(error, 'Login failed. Please check your credentials.')}
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            id="email"
            type="email"
            autoComplete="email"
            className="input mt-1"
            placeholder="Enter your email"
            {...register('email', { required: 'Email is required' })}
          />
          {errors.email && (
            <span className="text-red-600 text-xs">{errors.email.message}</span>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <input
            id="password"
            type="password"
            autoComplete="current-password"
            className="input mt-1"
            placeholder="Enter your password"
            {...register('password', { required: 'Password is required' })}
          />
          {errors.password && (
            <span className="text-red-600 text-xs">{errors.password.message}</span>
          )}
        </div>

        <button
          type="submit"
          className="btn-primary w-full mt-6"
          disabled={isLoading}
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          Regular admin?{' '}
          <Link to="/login" className="text-purple-600 hover:text-purple-700 font-medium">
            Admin Login
          </Link>
        </p>
      </div>
    </div>
  );
};

export default SuperAdminLogin;