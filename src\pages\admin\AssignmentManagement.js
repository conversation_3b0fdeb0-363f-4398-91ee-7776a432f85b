import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import AssignmentForm from '../../components/assignment/AssignmentForm';
import AssignmentCalendar from '../../components/assignment/AssignmentCalendar';
import AssignmentList from '../../components/assignment/AssignmentList';
import AssignmentFilters from '../../components/assignment/AssignmentFilters';
import { Calendar, List, Plus, Filter, Search } from 'lucide-react';
import {
  fetchAssignments,
  fetchEmployees,
  fetchLocations,
  createAssignment,
  updateAssignment,
  deleteAssignment,
  setFilters,
  setViewMode,
  setSelectedDate,
  selectFilteredAssignments,
  selectEmployees,
  selectLocations,
  selectFilters,
  selectViewMode,
  selectSelectedDate,
  selectLoading,
  selectCreateLoading,
  selectUpdateLoading,
  selectDeleteLoading
} from '../../store/slices/assignmentSlice';

const AssignmentManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  // Redux state
  const assignments = useSelector(selectFilteredAssignments);
  const employees = useSelector(selectEmployees);
  const locations = useSelector(selectLocations);
  const filters = useSelector(selectFilters);
  const viewMode = useSelector(selectViewMode);
  const selectedDate = useSelector(selectSelectedDate);
  const loading = useSelector(selectLoading);
  const createLoading = useSelector(selectCreateLoading);
  const updateLoading = useSelector(selectUpdateLoading);
  const deleteLoading = useSelector(selectDeleteLoading);

  // Local state
  const [showForm, setShowForm] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState(null);
  const [showFilters, setShowFilters] = useState(false);

  // Load data on component mount
  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchEmployees({ tenantId: tenant.id }));
      dispatch(fetchLocations({ tenantId: tenant.id }));
      dispatch(fetchAssignments({ tenantId: tenant.id, filters }));
    }
  }, [dispatch, tenant?.id]);

  // Reload assignments when filters change
  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchAssignments({ tenantId: tenant.id, filters }));
    }
  }, [dispatch, tenant?.id, filters]);

  const handleAddAssignment = () => {
    setEditingAssignment(null);
    setShowForm(true);
  };

  const handleEditAssignment = (assignment) => {
    setEditingAssignment(assignment);
    setShowForm(true);
  };

  const handleDeleteAssignment = async (assignmentId) => {
    if (window.confirm('Are you sure you want to delete this assignment?')) {
      await dispatch(deleteAssignment({ tenantId: tenant.id, assignmentId }));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingAssignment(null);
  };

  const handleFormSubmit = async (assignmentData) => {
    try {
      if (editingAssignment) {
        await dispatch(updateAssignment({
          tenantId: tenant.id,
          assignmentId: editingAssignment.id,
          assignmentData
        }));
      } else {
        await dispatch(createAssignment({
          tenantId: tenant.id,
          assignmentData
        }));
      }
      handleFormClose();
    } catch (error) {
      console.error('Error saving assignment:', error);
    }
  };

  const handleFiltersChange = (newFilters) => {
    dispatch(setFilters(newFilters));
  };

  const handleViewModeChange = (mode) => {
    dispatch(setViewMode(mode));
  };

  const handleDateChange = (date) => {
    dispatch(setSelectedDate(date.toISOString()));
  };

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading assignments...</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Assignment Management
        </h1>
        <p className="text-gray-600">
          Manage employee assignments and schedules for {tenant.name}
        </p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search assignments..."
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.search}
                onChange={(e) => handleFiltersChange({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4" />
              Filters
            </button>
          </div>

          {/* Add Assignment Button */}
          <BrandedButton
            onClick={handleAddAssignment}
            className="flex items-center gap-2"
            disabled={createLoading}
          >
            <Plus className="h-4 w-4" />
            {createLoading ? 'Adding...' : 'Add Assignment'}
          </BrandedButton>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <AssignmentFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              employees={employees}
              locations={locations}
            />
          </div>
        )}
      </div>

      {/* View Mode Toggle */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => handleViewModeChange('calendar')}
              className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'calendar'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Calendar className="h-4 w-4" />
              Calendar
            </button>
            <button
              onClick={() => handleViewModeChange('list')}
              className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <List className="h-4 w-4" />
              List
            </button>
          </div>

          {/* Date Navigation for Calendar */}
          {viewMode === 'calendar' && (
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  const newDate = new Date(selectedDate);
                  newDate.setDate(newDate.getDate() - 7);
                  handleDateChange(newDate);
                }}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
              >
                Previous Week
              </button>
              <span className="text-sm font-medium text-gray-700">
                {new Date(selectedDate).toLocaleDateString('en-US', { 
                  month: 'long', 
                  year: 'numeric' 
                })}
              </span>
              <button
                onClick={() => {
                  const newDate = new Date(selectedDate);
                  newDate.setDate(newDate.getDate() + 7);
                  handleDateChange(newDate);
                }}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
              >
                Next Week
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {viewMode === 'calendar' ? (
          <AssignmentCalendar
            assignments={assignments}
            employees={employees}
            locations={locations}
            selectedDate={new Date(selectedDate)}
            onDateChange={handleDateChange}
            onEdit={handleEditAssignment}
            onDelete={handleDeleteAssignment}
          />
        ) : (
          <AssignmentList
            assignments={assignments}
            employees={employees}
            locations={locations}
            onEdit={handleEditAssignment}
            onDelete={handleDeleteAssignment}
          />
        )}
      </div>

      {/* Assignment Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <AssignmentForm
              assignment={editingAssignment}
              employees={employees}
              locations={locations}
              tenantId={tenant.id}
              onSubmit={handleFormSubmit}
              onCancel={handleFormClose}
              loading={updateLoading || createLoading}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AssignmentManagement; 