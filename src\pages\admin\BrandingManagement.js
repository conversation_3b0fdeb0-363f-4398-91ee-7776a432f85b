import React, { useState } from 'react';

const BrandingManagement = () => {
  const [branding, setBranding] = useState({
    primaryColor: '#3b82f6',
    secondaryColor: '#10b981',
    logo: null,
    organizationName: 'Your Organization',
    tagline: 'Excellence in Motion',
    favicon: null
  });

  const handleColorChange = (key, value) => {
    setBranding(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleInputChange = (key, value) => {
    setBranding(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    alert('Branding settings saved successfully!');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Branding Management</h1>
        <button onClick={handleSave} className="btn-primary">
          Save Changes
        </button>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Organization Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Organization Name
            </label>
            <input
              type="text"
              className="input mt-1"
              value={branding.organizationName}
              onChange={(e) => handleInputChange('organizationName', e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Tagline
            </label>
            <input
              type="text"
              className="input mt-1"
              value={branding.tagline}
              onChange={(e) => handleInputChange('tagline', e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Color Scheme
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Primary Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={branding.primaryColor}
                onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                className="w-12 h-12 border border-gray-300 rounded-md cursor-pointer"
              />
              <input
                type="text"
                value={branding.primaryColor}
                onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                className="input flex-1"
                placeholder="#3b82f6"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Secondary Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={branding.secondaryColor}
                onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                className="w-12 h-12 border border-gray-300 rounded-md cursor-pointer"
              />
              <input
                type="text"
                value={branding.secondaryColor}
                onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                className="input flex-1"
                placeholder="#10b981"
              />
            </div>
          </div>
        </div>

        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 mb-3">Preview</h4>
          <div className="flex space-x-4">
            <div 
              className="w-20 h-20 rounded-lg flex items-center justify-center text-white font-bold"
              style={{ backgroundColor: branding.primaryColor }}
            >
              Primary
            </div>
            <div 
              className="w-20 h-20 rounded-lg flex items-center justify-center text-white font-bold"
              style={{ backgroundColor: branding.secondaryColor }}
            >
              Secondary
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Logo & Assets
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Organization Logo
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="flex text-sm text-gray-600">
                  <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                    <span>Upload a file</span>
                    <input type="file" className="sr-only" accept="image/*" />
                  </label>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
              </div>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Favicon
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="flex text-sm text-gray-600">
                  <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                    <span>Upload favicon</span>
                    <input type="file" className="sr-only" accept=".ico,.png" />
                  </label>
                </div>
                <p className="text-xs text-gray-500">ICO or PNG, 32x32px</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Live Preview
        </h3>
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-4">
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm"
              style={{ backgroundColor: branding.primaryColor }}
            >
              {branding.organizationName.charAt(0)}
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">{branding.organizationName}</h4>
              <p className="text-sm text-gray-500">{branding.tagline}</p>
            </div>
          </div>
          <button 
            className="px-4 py-2 rounded-md text-white font-medium"
            style={{ backgroundColor: branding.primaryColor }}
          >
            Sample Button
          </button>
        </div>
      </div>
    </div>
  );
};

export default BrandingManagement; 