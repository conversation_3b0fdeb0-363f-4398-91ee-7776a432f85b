import React from 'react';
import { useSelector } from 'react-redux';
import { 
  Users, 
  MapPin, 
  AlertTriangle, 
  BarChart3, 
  Calendar, 
  Clock,
  CheckCircle
} from 'lucide-react';
import { useTenant } from '../../tenant/TenantProvider';

const Dashboard = () => {
  const { tenant, isLoading } = useTenant();

  if (isLoading) {
    return <div className="p-8 text-center">Loading dashboard...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">
        {tenant?.branding?.dashboardTitle || tenant?.name || 'Tenant Dashboard'}
      </h1>
      <div className="mb-4">
        {tenant?.branding?.description ? (
          <p className="text-gray-700">{tenant.branding.description}</p>
        ) : (
          <p>Welcome to your organization dashboard. This is a placeholder for tenant-specific features.</p>
        )}
      </div>
      {/* Future: Client-specific settings, widgets, and features go here */}
      <div className="mt-8">
        <div className="bg-white rounded shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Organization Info</h2>
          <div>Name: <span className="font-mono">{tenant?.name}</span></div>
          <div>ID: <span className="font-mono">{tenant?.id}</span></div>
          {/* Future: Show more tenant-specific info and settings */}
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 