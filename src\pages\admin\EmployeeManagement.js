import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Plus, Search, Filter, Download, Upload, RefreshCw } from 'lucide-react';
import { fetchUsers, createUser, updateUser, deleteUser } from '../../store/slices/userSlice';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import EmployeeForm from '../../components/employee/EmployeeForm';
import EmployeeTable from '../../components/employee/EmployeeTable';
import EmployeeFilters from '../../components/employee/EmployeeFilters';

const EmployeeManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { users, isLoading, pagination } = useSelector((state) => state.user);
  const { user } = useSelector((state) => state.auth);

  const [showForm, setShowForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    role: 'all',
    department: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchUsers(tenant.id));
    }
  }, [dispatch, tenant?.id]);

  const handleAddEmployee = () => {
    setEditingEmployee(null);
    setShowForm(true);
  };

  const handleEditEmployee = (employee) => {
    setEditingEmployee(employee);
    setShowForm(true);
  };

  const handleDeleteEmployee = async (employeeId) => {
    if (window.confirm('Are you sure you want to delete this employee?')) {
      await dispatch(deleteUser({ tenantId: tenant.id, userId: employeeId }));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingEmployee(null);
  };

  const handleFormSubmit = () => {
    handleFormClose();
    // Refresh the employee list
    dispatch(fetchUsers(tenant.id));
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         user.email?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         user.employeeId?.toLowerCase().includes(filters.search.toLowerCase());
    const matchesStatus = filters.status === 'all' || user.status === filters.status;
    const matchesRole = filters.role === 'all' || user.role === filters.role;
    const matchesDepartment = filters.department === 'all' || user.department === filters.department;
    
    return matchesSearch && matchesStatus && matchesRole && matchesDepartment;
  });

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Employee Management
        </h1>
        <p className="text-gray-600">
          Manage employees for {tenant.name}
        </p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search employees..."
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4" />
              Filters
            </button>
          </div>

          {/* Add Employee Button */}
          <BrandedButton
            onClick={handleAddEmployee}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Employee
          </BrandedButton>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <EmployeeFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>
        )}
      </div>

      {/* Employee Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <EmployeeTable
          employees={filteredUsers}
          isLoading={isLoading}
          onEdit={handleEditEmployee}
          onDelete={handleDeleteEmployee}
          currentUser={user}
        />
      </div>

      {/* Employee Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <EmployeeForm
              employee={editingEmployee}
              tenantId={tenant.id}
              onSubmit={handleFormSubmit}
              onCancel={handleFormClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeManagement; 