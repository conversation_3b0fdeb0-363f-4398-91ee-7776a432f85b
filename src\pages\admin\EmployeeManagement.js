import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Users,
  UserPlus,
  Settings,
  Eye
} from 'lucide-react';
import { fetchUsers, createUser, updateUser, deleteUser } from '../../store/slices/userSlice';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import EmployeeForm from '../../components/employee/EmployeeForm';
import EmployeeTable from '../../components/employee/EmployeeTable';
import EmployeeFilters from '../../components/employee/EmployeeFilters';

const EmployeeManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { users, isLoading, pagination } = useSelector((state) => state.user);
  const { user } = useSelector((state) => state.auth);

  const [showForm, setShowForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    role: 'all',
    department: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchUsers(tenant.id));
    }
  }, [dispatch, tenant?.id]);

  const handleAddEmployee = () => {
    setEditingEmployee(null);
    setShowForm(true);
  };

  const handleEditEmployee = (employee) => {
    setEditingEmployee(employee);
    setShowForm(true);
  };

  const handleDeleteEmployee = async (employeeId) => {
    if (window.confirm('Are you sure you want to delete this employee?')) {
      await dispatch(deleteUser({ tenantId: tenant.id, userId: employeeId }));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingEmployee(null);
  };

  const handleFormSubmit = () => {
    handleFormClose();
    // Refresh the employee list
    dispatch(fetchUsers(tenant.id));
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         user.email?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         user.employeeId?.toLowerCase().includes(filters.search.toLowerCase());
    const matchesStatus = filters.status === 'all' || user.status === filters.status;
    const matchesRole = filters.role === 'all' || user.role === filters.role;
    const matchesDepartment = filters.department === 'all' || user.department === filters.department;
    
    return matchesSearch && matchesStatus && matchesRole && matchesDepartment;
  });

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500 text-white">
              <Users className="h-6 w-6" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Employee Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage employees for {tenant.name}
            </p>
          </div>
        </div>

        <button
          onClick={handleAddEmployee}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Add Employee
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-green-500 text-white">
                <Search className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Search & Filter</h3>
              <p className="text-sm text-gray-600">Find and filter employees</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search employees..."
                  className="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-3 border rounded-lg transition-colors ${
                  showFilters
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4" />
                Filters
              </button>
            </div>

            <div className="flex items-center space-x-3">
              <button className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Download className="h-4 w-4" />
                Export
              </button>
              <button className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Upload className="h-4 w-4" />
                Import
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <EmployeeFilters
                filters={filters}
                onFiltersChange={setFilters}
              />
            </div>
          )}
        </div>
      </div>

      {/* Employee Table */}
      <div className="bg-white shadow-lg rounded-xl border border-gray-200">
        <div className="px-6 py-6 sm:p-8">
          <div className="flex items-center mb-6">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-500 text-white">
                <Eye className="h-6 w-6" />
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-900">Employee List</h3>
              <p className="text-sm text-gray-600">
                {filteredUsers.length} employee{filteredUsers.length !== 1 ? 's' : ''} found
              </p>
            </div>
          </div>

          <EmployeeTable
            employees={filteredUsers}
            isLoading={isLoading}
            onEdit={handleEditEmployee}
            onDelete={handleDeleteEmployee}
            currentUser={user}
          />
        </div>
      </div>

      {/* Employee Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <EmployeeForm
              employee={editingEmployee}
              tenantId={tenant.id}
              onSubmit={handleFormSubmit}
              onCancel={handleFormClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeManagement; 