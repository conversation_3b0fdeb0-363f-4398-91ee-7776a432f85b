import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { MapPin, Plus, Search, Edit, Trash2, Eye, Filter, Users } from 'lucide-react';
import { fetchLocations, createLocation, updateLocation, deleteLocation } from '../../store/slices/locationSlice';
import { fetchUsers } from '../../store/slices/userSlice';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import LocationForm from '../../components/location/LocationForm';
import LocationTable from '../../components/location/LocationTable';
import LocationFilters from '../../components/location/LocationFilters';

const LocationManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);
  const { users, isLoading: usersLoading } = useSelector((state) => state.user);

  const [showForm, setShowForm] = useState(false);
  const [editingLocation, setEditingLocation] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    type: 'all',
    status: 'all',
    city: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch users when component mounts
  useEffect(() => {
    if (tenant?.id) {
      dispatch(fetchUsers(tenant.id));
    }
  }, [dispatch, tenant?.id]);

  // Mock data - replace with Redux state when location slice is created
  const [locations, setLocations] = useState([
    {
      id: '1',
      name: 'Downtown Office',
      address: '123 Main St, Downtown',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      type: 'office',
      status: 'active',
      contactPerson: 'John Doe',
      phone: '******-0123',
      email: '<EMAIL>',
      coordinates: { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: 'Warehouse Facility',
      address: '456 Industrial Blvd',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      type: 'warehouse',
      status: 'active',
      contactPerson: 'Jane Smith',
      phone: '******-0456',
      email: '<EMAIL>',
      coordinates: { lat: 34.0522, lng: -118.2437 }
    }
  ]);

  const handleAddLocation = () => {
    setEditingLocation(null);
    setShowForm(true);
  };

  const handleEditLocation = (location) => {
    setEditingLocation(location);
    setShowForm(true);
  };

  const handleDeleteLocation = async (locationId) => {
    if (window.confirm('Are you sure you want to delete this location?')) {
      setLocations(locations.filter(loc => loc.id !== locationId));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingLocation(null);
  };

  const handleFormSubmit = (locationData) => {
    if (editingLocation) {
      // Update existing location
      setLocations(locations.map(loc => 
        loc.id === editingLocation.id ? { ...locationData, id: loc.id } : loc
      ));
    } else {
      // Add new location
      const newLocation = {
        ...locationData,
        id: Date.now().toString()
      };
      setLocations([...locations, newLocation]);
    }
    handleFormClose();
  };

  const filteredLocations = locations.filter(location => {
    const matchesSearch = location.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         location.address?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         location.city?.toLowerCase().includes(filters.search.toLowerCase());
    const matchesType = filters.type === 'all' || location.type === filters.type;
    const matchesStatus = filters.status === 'all' || location.status === filters.status;
    const matchesCity = filters.city === 'all' || location.city === filters.city;
    
    return matchesSearch && matchesType && matchesStatus && matchesCity;
  });

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Location Management
        </h1>
        <p className="text-gray-600">
          Manage locations for {tenant.name}
        </p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search locations..."
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4" />
              Filters
            </button>
          </div>

          {/* Add Location Button */}
          <BrandedButton
            onClick={handleAddLocation}
            className="flex items-center gap-2"
          >
            <MapPin className="h-4 w-4" />
            Add Location
          </BrandedButton>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <LocationFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>
        )}
      </div>

      {/* Location Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <LocationTable
          locations={filteredLocations}
          isLoading={false}
          onEdit={handleEditLocation}
          onDelete={handleDeleteLocation}
          currentUser={user}
        />
      </div>

      {/* Users Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center mb-4">
            <Users className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Users</h3>
          </div>

          {usersLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading users...</p>
            </div>
          ) : users && users.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Phone
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <Users className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.firstName} {user.lastName}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.role === 'client_admin' ? 'bg-purple-100 text-purple-800' :
                          user.role === 'dispatcher' ? 'bg-blue-100 text-blue-800' :
                          user.role === 'supervisor' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {user.phone || 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="text-gray-500 mb-4">
                <Users className="h-12 w-12 mx-auto text-gray-300" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
              <p className="text-gray-600">No users have been added to this organization yet.</p>
            </div>
          )}
        </div>
      </div>

      {/* Location Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <LocationForm
              location={editingLocation}
              tenantId={tenant.id}
              onSubmit={handleFormSubmit}
              onCancel={handleFormClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationManagement; 