import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { MapPin, Plus, Search, Edit, Trash2, Eye, Filter } from 'lucide-react';
import { fetchLocations, createLocation, updateLocation, deleteLocation } from '../../store/slices/locationSlice';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import LocationForm from '../../components/location/LocationForm';
import LocationTable from '../../components/location/LocationTable';
import LocationFilters from '../../components/location/LocationFilters';

const LocationManagement = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  const [showForm, setShowForm] = useState(false);
  const [editingLocation, setEditingLocation] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    type: 'all',
    status: 'all',
    city: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Mock data - replace with Redux state when location slice is created
  const [locations, setLocations] = useState([
    {
      id: '1',
      name: 'Downtown Office',
      address: '123 Main St, Downtown',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      type: 'office',
      status: 'active',
      contactPerson: 'John Doe',
      phone: '******-0123',
      email: '<EMAIL>',
      coordinates: { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: 'Warehouse Facility',
      address: '456 Industrial Blvd',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      type: 'warehouse',
      status: 'active',
      contactPerson: 'Jane Smith',
      phone: '******-0456',
      email: '<EMAIL>',
      coordinates: { lat: 34.0522, lng: -118.2437 }
    }
  ]);

  const handleAddLocation = () => {
    setEditingLocation(null);
    setShowForm(true);
  };

  const handleEditLocation = (location) => {
    setEditingLocation(location);
    setShowForm(true);
  };

  const handleDeleteLocation = async (locationId) => {
    if (window.confirm('Are you sure you want to delete this location?')) {
      setLocations(locations.filter(loc => loc.id !== locationId));
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingLocation(null);
  };

  const handleFormSubmit = (locationData) => {
    if (editingLocation) {
      // Update existing location
      setLocations(locations.map(loc => 
        loc.id === editingLocation.id ? { ...locationData, id: loc.id } : loc
      ));
    } else {
      // Add new location
      const newLocation = {
        ...locationData,
        id: Date.now().toString()
      };
      setLocations([...locations, newLocation]);
    }
    handleFormClose();
  };

  const filteredLocations = locations.filter(location => {
    const matchesSearch = location.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         location.address?.toLowerCase().includes(filters.search.toLowerCase()) ||
                         location.city?.toLowerCase().includes(filters.search.toLowerCase());
    const matchesType = filters.type === 'all' || location.type === filters.type;
    const matchesStatus = filters.status === 'all' || location.status === filters.status;
    const matchesCity = filters.city === 'all' || location.city === filters.city;
    
    return matchesSearch && matchesType && matchesStatus && matchesCity;
  });

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Location Management
        </h1>
        <p className="text-gray-600">
          Manage locations for {tenant.name}
        </p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search locations..."
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4" />
              Filters
            </button>
          </div>

          {/* Add Location Button */}
          <BrandedButton
            onClick={handleAddLocation}
            className="flex items-center gap-2"
          >
            <MapPin className="h-4 w-4" />
            Add Location
          </BrandedButton>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <LocationFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>
        )}
      </div>

      {/* Location Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <LocationTable
          locations={filteredLocations}
          isLoading={false}
          onEdit={handleEditLocation}
          onDelete={handleDeleteLocation}
          currentUser={user}
        />
      </div>

      {/* Location Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <LocationForm
              location={editingLocation}
              tenantId={tenant.id}
              onSubmit={handleFormSubmit}
              onCancel={handleFormClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationManagement; 