import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTenant } from '../../tenant/TenantProvider';
import { useBranding } from '../../tenant/BrandingProvider';
import BrandedButton from '../../components/BrandedButton';
import ReportCard from '../../components/reports/ReportCard';
import ReportFilters from '../../components/reports/ReportFilters';
import ReportPreview from '../../components/reports/ReportPreview';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Filter, 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  AlertTriangle,
  FileText,
  PieChart,
  Activity,
  RefreshCw,
  XCircle
} from 'lucide-react';
import { 
  fetchReports, 
  generateReport, 
  downloadReport, 
  clearReportError 
} from '../../store/slices/reportSlice';

const ReportingAnalytics = () => {
  const dispatch = useDispatch();
  const { tenant } = useTenant();
  const { branding } = useBranding();
  const { user } = useSelector((state) => state.auth);

  // Local state for demo
  const [reportData, setReportData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [selectedReportType, setSelectedReportType] = useState('attendance');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [filters, setFilters] = useState({});
  const [generatedReport, setGeneratedReport] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [exportFormat, setExportFormat] = useState('pdf');

  // Report categories
  const reportCategories = [
    {
      id: 'attendance',
      title: 'Attendance Reports',
      description: 'Track employee attendance, punctuality, and time tracking',
      icon: Clock,
      color: 'bg-blue-500',
      reports: [
        {
          id: 'daily_attendance',
          title: 'Daily Attendance',
          description: 'Daily attendance summary by employee',
          type: 'table'
        },
        {
          id: 'attendance_summary',
          title: 'Attendance Summary',
          description: 'Monthly attendance overview with trends',
          type: 'chart'
        },
        {
          id: 'late_arrivals',
          title: 'Late Arrivals',
          description: 'Employees with frequent late arrivals',
          type: 'table'
        },
        {
          id: 'overtime_analysis',
          title: 'Overtime Analysis',
          description: 'Overtime hours and cost analysis',
          type: 'chart'
        }
      ]
    },
    {
      id: 'incidents',
      title: 'Incident Reports',
      description: 'Analyze incident patterns, severity, and resolution times',
      icon: AlertTriangle,
      color: 'bg-red-500',
      reports: [
        {
          id: 'incident_summary',
          title: 'Incident Summary',
          description: 'Overview of all incidents by type and severity',
          type: 'chart'
        },
        {
          id: 'incident_trends',
          title: 'Incident Trends',
          description: 'Incident frequency and patterns over time',
          type: 'chart'
        },
        {
          id: 'resolution_times',
          title: 'Resolution Times',
          description: 'Average time to resolve incidents by type',
          type: 'chart'
        },
        {
          id: 'incident_details',
          title: 'Incident Details',
          description: 'Detailed incident report with all information',
          type: 'table'
        }
      ]
    },
    {
      id: 'performance',
      title: 'Performance Reports',
      description: 'Employee performance metrics and productivity analysis',
      icon: TrendingUp,
      color: 'bg-green-500',
      reports: [
        {
          id: 'productivity_metrics',
          title: 'Productivity Metrics',
          description: 'Employee productivity and efficiency scores',
          type: 'chart'
        },
        {
          id: 'task_completion',
          title: 'Task Completion',
          description: 'Task completion rates and quality metrics',
          type: 'chart'
        },
        {
          id: 'performance_comparison',
          title: 'Performance Comparison',
          description: 'Compare employee performance across teams',
          type: 'chart'
        },
        {
          id: 'performance_details',
          title: 'Performance Details',
          description: 'Detailed performance breakdown by employee',
          type: 'table'
        }
      ]
    },
    {
      id: 'compliance',
      title: 'Compliance Reports',
      description: 'Regulatory compliance, safety standards, and audit reports',
      icon: PieChart,
      color: 'bg-purple-500',
      reports: [
        {
          id: 'safety_compliance',
          title: 'Safety Compliance',
          description: 'Safety protocol adherence and violations',
          type: 'chart'
        },
        {
          id: 'training_compliance',
          title: 'Training Compliance',
          description: 'Training completion and certification status',
          type: 'chart'
        },
        {
          id: 'audit_summary',
          title: 'Audit Summary',
          description: 'Compliance audit results and findings',
          type: 'table'
        },
        {
          id: 'violation_tracking',
          title: 'Violation Tracking',
          description: 'Track and analyze compliance violations',
          type: 'table'
        }
      ]
    }
  ];

  const loadReportData = async () => {
    try {
      setLoading(true);
      // Demo data
      setReportData({
        attendance: { totalHours: 1250, avgHours: 8.2 },
        incidents: { total: 12, resolved: 10 },
        performance: { efficiency: 85, completion: 92 },
        compliance: { score: 94, violations: 2 }
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // Demo export functionality
    alert('Report export functionality would be implemented here');
  };

  const handleReportSelect = (categoryId, reportId) => {
    const category = reportCategories.find(cat => cat.id === categoryId);
    const report = category?.reports.find(rep => rep.id === reportId);
    
    if (report) {
      setSelectedReportType(reportId);
    }
  };

  const handleGenerateReport = async () => {
    if (!selectedReportType) return;

    setLoading(true);
    try {
      await dispatch(generateReport({
        tenantId: tenant.id,
        reportType: selectedReportType,
        filters,
        format: 'pdf'
      }));
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    // Reload data with new filters
    setTimeout(() => loadReportData(), 100);
  };

  const getReportStats = () => {
    const stats = {
      totalEmployees: reportData.attendance?.totalEmployees || 0,
      averageAttendance: reportData.attendance?.averageAttendance || 0,
      totalIncidents: reportData.incidents?.totalIncidents || 0,
      resolvedIncidents: reportData.incidents?.resolvedIncidents || 0,
      averagePerformance: reportData.performance?.averagePerformance || 0,
      complianceScore: reportData.compliance?.complianceScore || 0
    };
    return stats;
  };

  // Load initial data
  useEffect(() => {
    loadReportData();
  }, []);

  if (!tenant?.id) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  const stats = getReportStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="p-8">
        {/* Enhanced Header */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-8 mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl shadow-lg">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Reporting & Analytics
              </h1>
              <p className="text-gray-600 mt-1">
                Generate comprehensive reports and analytics for {tenant.name}
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.totalEmployees}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-xl">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Attendance</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.averageAttendance}%</p>
              </div>
              <div className="p-3 bg-green-100 rounded-xl">
                <Clock className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Incidents</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.totalIncidents}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-xl">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved Incidents</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.resolvedIncidents}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-xl">
                <PieChart className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Performance</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.averagePerformance}%</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-xl">
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Compliance Score</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stats.complianceScore}%</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-xl">
                <PieChart className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Actions Bar */}
        <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-all duration-200 hover:border-gray-400"
              >
                <Filter className="h-5 w-5" />
                Filters
              </button>

              {/* Export Format */}
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400"
              >
                <option value="pdf">Export as PDF</option>
                <option value="csv">Export as CSV</option>
                <option value="excel">Export as Excel</option>
              </select>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <BrandedButton
                onClick={loadReportData}
                disabled={loading}
                className="flex items-center gap-2 px-6 py-3 shadow-lg"
              >
                <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </BrandedButton>

              {selectedReportType && (
                <>
                  <BrandedButton
                    onClick={handleGenerateReport}
                    disabled={loading}
                    className="flex items-center gap-2 px-6 py-3 shadow-lg"
                  >
                    <FileText className="h-5 w-5" />
                    Generate Report
                  </BrandedButton>

                  <BrandedButton
                    onClick={handleExportReport}
                    className="flex items-center gap-2 px-6 py-3 shadow-lg"
                  >
                    <Download className="h-5 w-5" />
                    Export
                  </BrandedButton>
                </>
              )}
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <ReportFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
            </div>
          )}
        </div>

        {/* Report Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {reportCategories.map((category) => (
            <div key={category.id} className="bg-white shadow-lg rounded-xl border border-gray-200">
              <ReportCard
                category={category}
                onReportSelect={handleReportSelect}
                selectedReport={selectedReportType}
                data={reportData[category.id]}
                loading={loading}
              />
            </div>
          ))}
        </div>

        {/* Report Preview */}
        {selectedReportType && (
          <div className="bg-white shadow-lg rounded-xl border border-gray-200 p-6">
            <ReportPreview
              report={selectedReportType}
              data={reportData[selectedReportType]}
              loading={loading}
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              <span>Error: {error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportingAnalytics; 