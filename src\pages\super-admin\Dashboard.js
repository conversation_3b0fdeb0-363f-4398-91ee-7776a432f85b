import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, 
  MapPin, 
  Users, 
  DollarSign, 
  AlertTriangle, 
  CreditCard,
  Settings,
  Eye
} from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchOrganizations } from '../../store/slices/organizationSlice';

const Dashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { organizations } = useSelector(state => state.organization);

  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  // Compute stats from organizations
  const totalOrganizations = organizations.length;
  const totalLocations = organizations.reduce((sum, org) => sum + (org.location_count || 0), 0);
  const totalUsers = organizations.reduce((sum, org) => sum + (org.user_count || 0), 0);
  const totalRevenue = organizations.reduce((sum, org) => {
    const revenue = parseFloat(org.monthly_revenue) || 0;
    return sum + revenue;
  }, 0);
  const monthlyGrowth = totalOrganizations > 0 ? Math.round((totalOrganizations / 10) * 100) / 10 : 0; // Simple growth calculation

  const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    // Generate recent activity based on organizations data
    const activities = [];

    if (organizations.length > 0) {
      // Add organization-based activities
      organizations.slice(0, 3).forEach((org, index) => {
        activities.push({
          id: index + 1,
          type: 'billing',
          message: `Payment received from ${org.name} ($${(org.monthly_revenue || 0).toFixed(2)})`,
          time: `${index + 1} ${index === 0 ? 'hour' : 'hours'} ago`
        });
      });

      // Add system activities
      activities.push(
        { id: 4, type: 'system', message: `${totalUsers} active users across ${totalOrganizations} organizations`, time: '1 day ago' },
        { id: 5, type: 'system', message: 'System health check completed successfully', time: '2 days ago' }
      );
    } else {
      // Default activities when no organizations
      activities.push(
        { id: 1, type: 'system', message: 'System initialized successfully', time: '1 hour ago' },
        { id: 2, type: 'system', message: 'Database connection established', time: '2 hours ago' }
      );
    }

    setRecentActivity(activities);
  }, [organizations, totalUsers, totalOrganizations]);

  const getBillingStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'new_org': return <Building2 className="h-4 w-4" />;
      case 'billing': return <DollarSign className="h-4 w-4" />;
      case 'incident': return <AlertTriangle className="h-4 w-4" />;
      case 'user': return <Users className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-600">Platform overview and management</p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Organizations</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{totalOrganizations}</div>
                    <div className="ml-2 text-sm font-medium text-green-600">+{monthlyGrowth}%</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MapPin className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Locations</dt>
                  <dd className="text-2xl font-semibold text-gray-900">{totalLocations}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-2xl font-semibold text-gray-900">{totalUsers}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    ${(typeof totalRevenue === 'number' ? totalRevenue : 0).toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Organizations Overview */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Organizations Overview</h3>
            <button 
              onClick={() => navigate('/super-admin/organizations')}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View All →
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Organization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Locations
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Users
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Incidents
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Billing
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Revenue
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {organizations.map((org) => (
                  <tr key={org.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{org.name}</div>
                        <div className="text-sm text-gray-500">{org.contact_person || org.email}</div>
                        <div className="text-xs text-blue-600 capitalize">{org.subscription_plan || 'basic'} plan</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {org.location_count || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {org.user_count || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        (org.incidents_count || 0) > 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {org.incidents_count || 0}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBillingStatusColor(org.billing_status)}`}>
                        {org.billing_status || 'pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${(org.monthly_revenue || 0).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        onClick={() => navigate(`/super-admin/organizations/${org.id}`)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      {getActivityIcon(activity.type)}
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button 
                onClick={() => navigate('/super-admin/organizations')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
              >
                <Building2 className="h-6 w-6 text-blue-600 mb-2" />
                <div className="font-medium text-gray-900">Manage Organizations</div>
                <div className="text-sm text-gray-500">View and manage all client organizations</div>
              </button>
              

              
              <button 
                onClick={() => navigate('/super-admin/billing')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
              >
                <CreditCard className="h-6 w-6 text-yellow-600 mb-2" />
                <div className="font-medium text-gray-900">Billing & Invoicing</div>
                <div className="text-sm text-gray-500">Manage billing and generate invoices</div>
              </button>
              
              <button 
                onClick={() => navigate('/super-admin/settings')}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
              >
                <Settings className="h-6 w-6 text-purple-600 mb-2" />
                <div className="font-medium text-gray-900">System Settings</div>
                <div className="text-sm text-gray-500">Configure platform-wide settings</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 