import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  ArrowLeft, 
  Save, 
  Building2, 
  Mail, 
  Phone, 
  MapPin,
  User,
  CreditCard,
  Loader2
} from 'lucide-react';
import { createOrganization, updateOrganization, fetchOrganizationById } from '../../store/slices/organizationSlice';
import { renderErrorMessage } from '../../utils/errorUtils';

// Suppress ResizeObserver errors for this component
const suppressResizeObserverErrors = () => {
  const originalError = console.error;
  console.error = (...args) => {
    if (typeof args[0] === 'string' && args[0].includes('ResizeObserver')) {
      return;
    }
    originalError.apply(console, args);
  };
};

const OrganizationForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isEditing = Boolean(id);
  
  // Redux state
  const { currentOrganization, isLoading, error } = useSelector(state => state.organization);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    contactPerson: '',
    contactEmail: '',
    contactPhone: '',
    subscriptionPlan: 'basic',
    billingAddress: '',
    paymentMethod: 'invoice',
    maxUsers: 100,
    maxLocations: 10,
    status: 'active'
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Suppress ResizeObserver errors when component mounts
    suppressResizeObserverErrors();
    
    if (isEditing && id) {
      // Fetch organization data for editing
      dispatch(fetchOrganizationById(id));
    }
  }, [isEditing, id, dispatch]);

  useEffect(() => {
    if (isEditing && currentOrganization) {
      // Populate form with fetched organization data
      setFormData({
        name: currentOrganization.name || '',
        email: currentOrganization.email || '',
        phone: currentOrganization.phone || '',
        address: currentOrganization.address || '',
        contactPerson: currentOrganization.contact_person || '',
        contactEmail: currentOrganization.contact_email || '',
        contactPhone: currentOrganization.contact_phone || '',
        subscriptionPlan: currentOrganization.subscription_plan || 'basic',
        billingAddress: currentOrganization.billing_address || '',
        paymentMethod: currentOrganization.payment_method || 'invoice',
        maxUsers: currentOrganization.max_users || 100,
        maxLocations: currentOrganization.max_locations || 10,
        status: currentOrganization.status || 'active'
      });
    }
  }, [isEditing, currentOrganization]);

  const handleInputChange = (field, value) => {
    try {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({
          ...prev,
          [field]: null
        }));
      }
    } catch (error) {
      // Ignore ResizeObserver errors
      if (!error.message?.includes('ResizeObserver')) {
        console.error('Error in handleInputChange:', error);
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Organization name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }
    
    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }
    
    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'Contact person is required';
    }
    
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = 'Contact email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.contactEmail)) {
      newErrors.contactEmail = 'Contact email is invalid';
    }
    
    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Contact phone is required';
    }
    
    if (formData.maxUsers < 1) {
      newErrors.maxUsers = 'Max users must be at least 1';
    }
    
    if (formData.maxLocations < 1) {
      newErrors.maxLocations = 'Max locations must be at least 1';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const organizationData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        contactPerson: formData.contactPerson,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone,
        subscriptionPlan: formData.subscriptionPlan,
        billingAddress: formData.billingAddress,
        paymentMethod: formData.paymentMethod,
        maxUsers: parseInt(formData.maxUsers),
        maxLocations: parseInt(formData.maxLocations),
        status: formData.status
      };
      
      if (isEditing) {
        await dispatch(updateOrganization({ organizationId: id, organizationData: organizationData })).unwrap();
      } else {
        await dispatch(createOrganization(organizationData)).unwrap();
      }
      
      // Navigate back to organizations list with a small delay to prevent ResizeObserver errors
      setTimeout(() => {
        navigate('/super-admin/organizations');
      }, 100);
      
    } catch (error) {
      // Ignore ResizeObserver errors but handle other errors
      if (error.message?.includes('ResizeObserver')) {
        // Still navigate on ResizeObserver errors as they don't affect functionality
        setTimeout(() => {
          navigate('/super-admin/organizations');
        }, 100);
        return;
      }
      
      console.error('Error saving organization:', error);
      setErrors({ submit: renderErrorMessage(error, 'Failed to save organization. Please try again.') });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading && isEditing) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Loading organization...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button 
            onClick={() => navigate('/super-admin/organizations')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Organization' : 'Add New Organization'}
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              {isEditing ? 'Update organization details' : 'Create a new organization in the system'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => navigate('/super-admin/organizations')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="organization-form"
            disabled={isSubmitting}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEditing ? 'Update Organization' : 'Create Organization'}
              </>
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">
            {renderErrorMessage(error, 'An error occurred while processing your request.')}
          </p>
        </div>
      )}

      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      {/* Form */}
      <form id="organization-form" onSubmit={handleSubmit} className="space-y-8">
        {/* Organization Information */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center mb-4">
              <Building2 className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Organization Information</h3>
            </div>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Organization Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.name ? 'border-red-300' : ''
                  }`}
                  placeholder="Enter organization name"
                />
                {errors.name && <p className="mt-2 text-sm text-red-600">{errors.name}</p>}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email *
                </label>
                <div className="mt-1 relative">
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                      errors.email ? 'border-red-300' : ''
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.email && <p className="mt-2 text-sm text-red-600">{errors.email}</p>}
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                  Phone Number *
                </label>
                <div className="mt-1 relative">
                  <input
                    type="tel"
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className={`block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                      errors.phone ? 'border-red-300' : ''
                    }`}
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.phone && <p className="mt-2 text-sm text-red-600">{errors.phone}</p>}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </select>
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address *
                </label>
                <div className="mt-1 relative">
                  <textarea
                    id="address"
                    rows={3}
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className={`block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                      errors.address ? 'border-red-300' : ''
                    }`}
                    placeholder="Enter organization address"
                  />
                  <div className="absolute top-3 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.address && <p className="mt-2 text-sm text-red-600">{errors.address}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center mb-4">
              <User className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
            </div>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700">
                  Contact Person *
                </label>
                <input
                  type="text"
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.contactPerson ? 'border-red-300' : ''
                  }`}
                  placeholder="Primary contact name"
                />
                {errors.contactPerson && <p className="mt-2 text-sm text-red-600">{errors.contactPerson}</p>}
              </div>

              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">
                  Contact Email *
                </label>
                <div className="mt-1 relative">
                  <input
                    type="email"
                    id="contactEmail"
                    value={formData.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                    className={`block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                      errors.contactEmail ? 'border-red-300' : ''
                    }`}
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.contactEmail && <p className="mt-2 text-sm text-red-600">{errors.contactEmail}</p>}
              </div>

              <div>
                <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700">
                  Contact Phone *
                </label>
                <div className="mt-1 relative">
                  <input
                    type="tel"
                    id="contactPhone"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                    className={`block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                      errors.contactPhone ? 'border-red-300' : ''
                    }`}
                    placeholder="+****************"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {errors.contactPhone && <p className="mt-2 text-sm text-red-600">{errors.contactPhone}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Subscription & Billing */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center mb-4">
              <CreditCard className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Subscription & Billing</h3>
            </div>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="subscriptionPlan" className="block text-sm font-medium text-gray-700">
                  Subscription Plan
                </label>
                <select
                  id="subscriptionPlan"
                  value={formData.subscriptionPlan}
                  onChange={(e) => handleInputChange('subscriptionPlan', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="basic">Basic</option>
                  <option value="professional">Professional</option>
                  <option value="enterprise">Enterprise</option>
                </select>
              </div>

              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="invoice">Invoice</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>

              <div>
                <label htmlFor="maxUsers" className="block text-sm font-medium text-gray-700">
                  Maximum Users
                </label>
                <input
                  type="number"
                  id="maxUsers"
                  min="1"
                  value={formData.maxUsers}
                  onChange={(e) => handleInputChange('maxUsers', parseInt(e.target.value) || 0)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.maxUsers ? 'border-red-300' : ''
                  }`}
                />
                {errors.maxUsers && <p className="mt-2 text-sm text-red-600">{errors.maxUsers}</p>}
              </div>

              <div>
                <label htmlFor="maxLocations" className="block text-sm font-medium text-gray-700">
                  Maximum Locations
                </label>
                <input
                  type="number"
                  id="maxLocations"
                  min="1"
                  value={formData.maxLocations}
                  onChange={(e) => handleInputChange('maxLocations', parseInt(e.target.value) || 0)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.maxLocations ? 'border-red-300' : ''
                  }`}
                />
                {errors.maxLocations && <p className="mt-2 text-sm text-red-600">{errors.maxLocations}</p>}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="billingAddress" className="block text-sm font-medium text-gray-700">
                  Billing Address
                </label>
                <textarea
                  id="billingAddress"
                  rows={3}
                  value={formData.billingAddress}
                  onChange={(e) => handleInputChange('billingAddress', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Billing address (if different from organization address)"
                />
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default OrganizationForm;

