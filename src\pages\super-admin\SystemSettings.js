import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Globe, 
  Shield, 
  Bell, 
  CreditCard, 
  Database,
  Mail,
  Smartphone,
  Key,
  Lock,
  Eye,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

const SystemSettings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // TODO: Load system settings from database
    // For now, using default values until API is implemented
    setSettings({
      general: {
        platformName: 'FootOnStreet Security',
        supportEmail: '<EMAIL>',
        maintenanceMode: false,
        allowRegistration: true,
        defaultTimezone: 'UTC',
        sessionTimeout: 30,
        maxLoginAttempts: 5
      },
      billing: {
        currency: 'USD',
        taxRate: 8.5,
        gracePeriod: 7,
        autoSuspendOverdue: true,
        paymentGateway: 'stripe',
        stripePublicKey: 'pk_test_...',
        stripeSecretKey: '••••••••••••••••'
      },
      notifications: {
        emailEnabled: true,
        smsEnabled: true,
        emailProvider: 'sendgrid',
        smsProvider: 'twilio',
        sendgridApiKey: '••••••••••••••••',
        twilioAccountSid: '••••••••••••••••',
        twilioAuthToken: '••••••••••••••••'
      },
      security: {
        enforcePasswordPolicy: true,
        minPasswordLength: 8,
        requireSpecialChars: true,
        requireNumbers: true,
        enableTwoFactor: true,
        sessionSecurityLevel: 'high',
        apiRateLimit: 1000,
        enableAuditLog: true
      },
      integrations: {
        recaptchaEnabled: true,
        recaptchaSiteKey: '••••••••••••••••',
        recaptchaSecretKey: '••••••••••••••••',
        googleMapsApiKey: '••••••••••••••••',
        analyticsEnabled: true,
        analyticsTrackingId: 'GA-•••••••••'
      }
    });
  }, []);

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    console.log('Saving settings:', settings);
    setHasChanges(false);
    // Implement save functionality
  };

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'billing', name: 'Billing', icon: CreditCard },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'integrations', name: 'Integrations', icon: Globe }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Configure platform-wide settings and integrations</p>
        </div>
        {hasChanges && (
          <button 
            onClick={handleSave}
            className="btn-primary flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            Save Changes
          </button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">General Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Platform Name</label>
                  <input
                    type="text"
                    value={settings.general?.platformName || ''}
                    onChange={(e) => handleSettingChange('general', 'platformName', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Support Email</label>
                  <input
                    type="email"
                    value={settings.general?.supportEmail || ''}
                    onChange={(e) => handleSettingChange('general', 'supportEmail', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Default Timezone</label>
                  <select
                    value={settings.general?.defaultTimezone || 'UTC'}
                    onChange={(e) => handleSettingChange('general', 'defaultTimezone', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    value={settings.general?.sessionTimeout || 30}
                    onChange={(e) => handleSettingChange('general', 'sessionTimeout', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.general?.maintenanceMode || false}
                    onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Maintenance Mode</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.general?.allowRegistration || false}
                    onChange={(e) => handleSettingChange('general', 'allowRegistration', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Allow New Organization Registration</label>
                </div>
              </div>
            </div>
          )}

          {/* Billing Settings */}
          {activeTab === 'billing' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Billing Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Currency</label>
                  <select
                    value={settings.billing?.currency || 'USD'}
                    onChange={(e) => handleSettingChange('billing', 'currency', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                  <input
                    type="number"
                    step="0.1"
                    value={settings.billing?.taxRate || 0}
                    onChange={(e) => handleSettingChange('billing', 'taxRate', parseFloat(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Grace Period (days)</label>
                  <input
                    type="number"
                    value={settings.billing?.gracePeriod || 7}
                    onChange={(e) => handleSettingChange('billing', 'gracePeriod', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Gateway</label>
                  <select
                    value={settings.billing?.paymentGateway || 'stripe'}
                    onChange={(e) => handleSettingChange('billing', 'paymentGateway', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="stripe">Stripe</option>
                    <option value="paypal">PayPal</option>
                    <option value="square">Square</option>
                  </select>
                </div>
              </div>
              
              {settings.billing?.paymentGateway === 'stripe' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stripe Public Key</label>
                    <input
                      type="text"
                      value={settings.billing?.stripePublicKey || ''}
                      onChange={(e) => handleSettingChange('billing', 'stripePublicKey', e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stripe Secret Key</label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="password"
                        value={settings.billing?.stripeSecretKey || ''}
                        onChange={(e) => handleSettingChange('billing', 'stripeSecretKey', e.target.value)}
                        className="flex-1 border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 hover:bg-gray-100">
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Security Settings */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Security Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Minimum Password Length</label>
                  <input
                    type="number"
                    value={settings.security?.minPasswordLength || 8}
                    onChange={(e) => handleSettingChange('security', 'minPasswordLength', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">API Rate Limit (requests/hour)</label>
                  <input
                    type="number"
                    value={settings.security?.apiRateLimit || 1000}
                    onChange={(e) => handleSettingChange('security', 'apiRateLimit', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.security?.enforcePasswordPolicy || false}
                    onChange={(e) => handleSettingChange('security', 'enforcePasswordPolicy', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Enforce Strong Password Policy</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.security?.enableTwoFactor || false}
                    onChange={(e) => handleSettingChange('security', 'enableTwoFactor', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Enable Two-Factor Authentication</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.security?.enableAuditLog || false}
                    onChange={(e) => handleSettingChange('security', 'enableAuditLog', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Enable Audit Logging</label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemSettings; 