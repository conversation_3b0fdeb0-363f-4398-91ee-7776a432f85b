import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';
import LandingPage from '../pages/LandingPage';
import Login from '../pages/Login';
import SuperAdminLogin from '../pages/SuperAdminLogin';
import TestLogin from '../TestLogin';
import Dashboard from '../pages/admin/Dashboard';
import BrandingManagement from '../pages/admin/BrandingManagement';
import EmployeeManagement from '../pages/admin/EmployeeManagement';
import LocationManagement from '../pages/admin/LocationManagement';
import AssignmentManagement from '../pages/admin/AssignmentManagement';
import LiveMapDashboard from '../pages/admin/LiveMapDashboard';
import IncidentManagement from '../pages/admin/IncidentManagement';
import ReportingAnalytics from '../pages/admin/ReportingAnalytics';
import GeofencingManagement from '../pages/admin/GeofencingManagement';
import NotFound from '../pages/NotFound';

// Super Admin Components
import SuperAdminDashboard from '../pages/super-admin/Dashboard';
import OrganizationManagement from '../pages/super-admin/OrganizationManagement';
import OrganizationDetails from '../pages/super-admin/OrganizationDetails';
import OrganizationForm from '../pages/super-admin/OrganizationForm';

import BillingManagement from '../pages/super-admin/BillingManagement';
import SystemSettings from '../pages/super-admin/SystemSettings';
import LocationDetails from '../pages/super-admin/LocationDetails';

// Route guard component
const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
    return <Navigate to="/admin/dashboard" replace />;
  }
  
  return children;
};

// Admin route guard (for non-super admin users)
const AdminRoute = ({ children }) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (user?.role === 'super_admin') {
    return <Navigate to="/super-admin/dashboard" replace />;
  }
  
  return children;
};

// Super Admin route guard
const SuperAdminRoute = ({ children }) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  
  if (!isAuthenticated) {
    return <Navigate to="/super-admin-login" replace />;
  }
  
  if (user?.role !== 'super_admin') {
    return <Navigate to="/admin/dashboard" replace />;
  }
  
  return children;
};

const AppRoutes = () => (
  <Suspense fallback={<div className="p-8 text-center">Loading...</div>}>
    <Routes>
      {/* Landing Page - Default Route */}
      <Route path="/" element={<LandingPage />} />
      
      {/* Authentication Routes */}
      <Route path="/login" element={
        <AuthLayout>
          <Login />
        </AuthLayout>
      } />
      <Route path="/super-admin-login" element={
        <AuthLayout>
          <SuperAdminLogin />
        </AuthLayout>
      } />
      <Route path="/test-login" element={<TestLogin />} />
      
      {/* Super Admin Routes */}
      <Route path="/super-admin" element={
        <SuperAdminRoute>
          <MainLayout />
        </SuperAdminRoute>
      }>
        <Route index element={<Navigate to="/super-admin/dashboard" replace />} />
        <Route path="dashboard" element={<SuperAdminDashboard />} />
        <Route path="organizations" element={<OrganizationManagement />} />
        <Route path="organizations/new" element={<OrganizationForm />} />
        <Route path="organizations/:id" element={<OrganizationDetails />} />
        <Route path="organizations/:id/edit" element={<OrganizationForm />} />
        <Route path="organizations/:orgId/locations/:locationId" element={<LocationDetails />} />

        <Route path="billing" element={<BillingManagement />} />
        <Route path="settings" element={<SystemSettings />} />
      </Route>
      
      {/* Admin Routes */}
      <Route path="/admin" element={
        <AdminRoute>
          <MainLayout />
        </AdminRoute>
      }>
        <Route index element={<Navigate to="/admin/dashboard" replace />} />
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="branding" element={
          <ProtectedRoute allowedRoles={['client_admin']}>
            <BrandingManagement />
          </ProtectedRoute>
        } />
        <Route path="employees" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher']}>
            <EmployeeManagement />
          </ProtectedRoute>
        } />
        <Route path="locations" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher']}>
            <LocationManagement />
          </ProtectedRoute>
        } />
        <Route path="assignments" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher', 'supervisor']}>
            <AssignmentManagement />
          </ProtectedRoute>
        } />
        <Route path="live-map" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher', 'supervisor']}>
            <LiveMapDashboard />
          </ProtectedRoute>
        } />
        <Route path="incidents" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher', 'supervisor']}>
            <IncidentManagement />
          </ProtectedRoute>
        } />
        <Route path="reports" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher']}>
            <ReportingAnalytics />
          </ProtectedRoute>
        } />
        <Route path="geofencing" element={
          <ProtectedRoute allowedRoles={['client_admin', 'dispatcher']}>
            <GeofencingManagement />
          </ProtectedRoute>
        } />
      </Route>
      
      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  </Suspense>
);

export default AppRoutes;