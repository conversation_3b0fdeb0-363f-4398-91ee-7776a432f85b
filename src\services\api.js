import axios from 'axios';
import { store } from '../store';
import { logout, refreshToken } from '../store/slices/authSlice';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = store.getState().auth.token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add tenant ID to headers if available
    const tenant = store.getState().auth.tenant;
    if (tenant?.id) {
      config.headers['X-Tenant-ID'] = tenant.id;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshTokenValue = store.getState().auth.refreshToken;
        if (refreshTokenValue) {
          const response = await api.post('/auth/refresh', {
            refreshToken: refreshTokenValue,
          });
          
          const { token, refreshToken: newRefreshToken } = response.data;
          store.dispatch(refreshToken.fulfilled({ token, refreshToken: newRefreshToken }));
          
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        store.dispatch(logout.fulfilled());
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
  changePassword: (passwords) => api.post('/auth/change-password', passwords),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
};

// Organization API
export const organizationAPI = {
  getOrganizations: () => api.get('/organizations'),
  getOrganizationById: (organizationId) => api.get(`/organizations/${organizationId}`),
  createOrganization: (organizationData) => api.post('/organizations', organizationData),
  updateOrganization: (organizationId, organizationData) => api.put(`/organizations/${organizationId}`, organizationData),
  deleteOrganization: (organizationId) => api.delete(`/organizations/${organizationId}`),
  getOrganizationStats: (organizationId) => api.get(`/organizations/${organizationId}/stats`),
  updateOrganizationBranding: (organizationId, brandingData) => 
    api.put(`/organizations/${organizationId}/branding`, brandingData),
};

// User API
export const userAPI = {
  getUsers: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/users`, { params }),
  getUserById: (organizationId, userId) => 
    api.get(`/organizations/${organizationId}/users/${userId}`),
  createUser: (organizationId, userData) => 
    api.post(`/organizations/${organizationId}/users`, userData),
  updateUser: (organizationId, userId, userData) => 
    api.put(`/organizations/${organizationId}/users/${userId}`, userData),
  deleteUser: (organizationId, userId) => 
    api.delete(`/organizations/${organizationId}/users/${userId}`),
  getUserLocations: (organizationId, userId, params = {}) => 
    api.get(`/organizations/${organizationId}/users/${userId}/locations`, { params }),
  getUserStats: (organizationId, userId) => 
    api.get(`/organizations/${organizationId}/users/${userId}/stats`),
  assignUserToLocation: (organizationId, userId, locationId) => 
    api.post(`/organizations/${organizationId}/users/${userId}/assignments`, { locationId }),
};

// Location API
export const locationAPI = {
  getLocations: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/locations`, { params }),
  getLocationById: (organizationId, locationId) => 
    api.get(`/organizations/${organizationId}/locations/${locationId}`),
  createLocation: (organizationId, locationData) => 
    api.post(`/organizations/${organizationId}/locations`, locationData),
  updateLocation: (organizationId, locationId, locationData) => 
    api.put(`/organizations/${organizationId}/locations/${locationId}`, locationData),
  deleteLocation: (organizationId, locationId) => 
    api.delete(`/organizations/${organizationId}/locations/${locationId}`),
  getLocationAssignments: (organizationId, locationId) => 
    api.get(`/organizations/${organizationId}/locations/${locationId}/assignments`),
};

// Assignment API
export const assignmentAPI = {
  getAssignments: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/assignments`, { params }),
  getAssignmentById: (organizationId, assignmentId) => 
    api.get(`/organizations/${organizationId}/assignments/${assignmentId}`),
  createAssignment: (organizationId, assignmentData) => 
    api.post(`/organizations/${organizationId}/assignments`, assignmentData),
  updateAssignment: (organizationId, assignmentId, assignmentData) => 
    api.put(`/organizations/${organizationId}/assignments/${assignmentId}`, assignmentData),
  deleteAssignment: (organizationId, assignmentId) => 
    api.delete(`/organizations/${organizationId}/assignments/${assignmentId}`),
};

// Incident API
export const incidentAPI = {
  getIncidents: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/incidents`, { params }),
  getIncidentById: (organizationId, incidentId) => 
    api.get(`/organizations/${organizationId}/incidents/${incidentId}`),
  createIncident: (organizationId, incidentData) => 
    api.post(`/organizations/${organizationId}/incidents`, incidentData),
  updateIncident: (organizationId, incidentId, incidentData) => 
    api.put(`/organizations/${organizationId}/incidents/${incidentId}`, incidentData),
  deleteIncident: (organizationId, incidentId) => 
    api.delete(`/organizations/${organizationId}/incidents/${incidentId}`),
  resolveIncident: (organizationId, incidentId, resolutionData) => 
    api.post(`/organizations/${organizationId}/incidents/${incidentId}/resolve`, resolutionData),
};

// Report API
export const reportAPI = {
  getAttendanceReport: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/reports/attendance`, { params }),
  getSafetyReport: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/reports/safety`, { params }),
  getPerformanceReport: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/reports/performance`, { params }),
  exportReport: (organizationId, reportType, params = {}) => 
    api.get(`/organizations/${organizationId}/reports/${reportType}/export`, { 
      params,
      responseType: 'blob'
    }),
};

// Messaging API
export const messagingAPI = {
  getMessages: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/messages`, { params }),
  sendMessage: (organizationId, messageData) => 
    api.post(`/organizations/${organizationId}/messages`, messageData),
  markAsRead: (organizationId, messageId) => 
    api.put(`/organizations/${organizationId}/messages/${messageId}/read`),
  deleteMessage: (organizationId, messageId) => 
    api.delete(`/organizations/${organizationId}/messages/${messageId}`),
  getConversations: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/conversations`, { params }),
  createConversation: (organizationId, conversationData) => 
    api.post(`/organizations/${organizationId}/conversations`, conversationData),
  markConversationAsRead: (organizationId, conversationId) => 
    api.put(`/organizations/${organizationId}/conversations/${conversationId}/read`),
  markMessageAsRead: (organizationId, messageId) => 
    api.put(`/organizations/${organizationId}/messages/${messageId}/read`),
};

// Geofence API
export const geofenceAPI = {
  getGeofences: (organizationId, params = {}) => 
    api.get(`/organizations/${organizationId}/geofences`, { params }),
  getGeofenceById: (organizationId, geofenceId) => 
    api.get(`/organizations/${organizationId}/geofences/${geofenceId}`),
  createGeofence: (organizationId, geofenceData) => 
    api.post(`/organizations/${organizationId}/geofences`, geofenceData),
  updateGeofence: (organizationId, geofenceId, geofenceData) => 
    api.put(`/organizations/${organizationId}/geofences/${geofenceId}`, geofenceData),
  deleteGeofence: (organizationId, geofenceId) => 
    api.delete(`/organizations/${organizationId}/geofences/${geofenceId}`),
  getGeofenceAlerts: (organizationId, geofenceId, params = {}) => 
    api.get(`/organizations/${organizationId}/geofences/${geofenceId}/alerts`, { params }),
  acknowledgeAlert: (organizationId, alertId) => 
    api.put(`/organizations/${organizationId}/geofence-alerts/${alertId}/acknowledge`),
  checkViolation: (organizationId, data) => 
    api.post(`/organizations/${organizationId}/geofences/check-violation`, data),
};

export default api; 