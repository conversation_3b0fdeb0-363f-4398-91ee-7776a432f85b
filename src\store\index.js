import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import organizationReducer from './slices/organizationSlice';
import userReducer from './slices/userSlice';
import uiReducer from './slices/uiSlice';
import employeeReducer from './slices/employeeSlice';
import locationReducer from './slices/locationSlice';
import assignmentReducer from './slices/assignmentSlice';
import mapReducer from './slices/mapSlice';
import incidentReducer from './slices/incidentSlice';
import messagingReducer from './slices/messagingSlice';
import notificationReducer from './slices/notificationSlice';
import reportReducer from './slices/reportSlice';
import geofenceReducer from './slices/geofenceSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    organization: organizationReducer,
    user: userReducer,
    ui: uiReducer,
    employees: employeeReducer,
    locations: locationReducer,
    assignments: assignmentReducer,
    map: mapReducer,
    incidents: incidentReducer,
    messaging: messagingReducer,
    notifications: notificationReducer,
    reports: reportReducer,
    geofences: geofenceReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export default store; 