import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Async thunks
export const fetchAssignments = createAsyncThunk(
  'assignments/fetchAssignments',
  async ({ organizationId, filters = {} }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/organizations/${organizationId}/assignments`, { params: filters });
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch assignments');
    }
  }
);

export const createAssignment = createAsyncThunk(
  'assignments/createAssignment',
  async ({ organizationId, assignmentData }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/organizations/${organizationId}/assignments`, assignmentData);
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to create assignment');
    }
  }
);

export const updateAssignment = createAsyncThunk(
  'assignments/updateAssignment',
  async ({ organizationId, assignmentId, assignmentData }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/organizations/${organizationId}/assignments/${assignmentId}`, assignmentData);
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to update assignment');
    }
  }
);

export const deleteAssignment = createAsyncThunk(
  'assignments/deleteAssignment',
  async ({ organizationId, assignmentId }, { rejectWithValue }) => {
    try {
      await api.delete(`/organizations/${organizationId}/assignments/${assignmentId}`);
      return assignmentId;
    } catch (error) {
      return rejectWithValue('Failed to delete assignment');
    }
  }
);

export const fetchEmployees = createAsyncThunk(
  'assignments/fetchEmployees',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/organizations/${organizationId}/employees`);
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch employees');
    }
  }
);

export const fetchLocations = createAsyncThunk(
  'assignments/fetchLocations',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/organizations/${organizationId}/locations`);
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch locations');
    }
  }
);

const initialState = {
  assignments: [],
  employees: [],
  locations: [],
  filters: {
    search: '',
    employee: 'all',
    location: 'all',
    status: 'all',
    dateRange: 'week'
  },
  viewMode: 'calendar', // 'calendar' or 'list'
  selectedDate: new Date().toISOString(),
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false
};

const assignmentSlice = createSlice({
  name: 'assignments',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        employee: 'all',
        location: 'all',
        status: 'all',
        dateRange: 'week'
      };
    },
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },
    setSelectedDate: (state, action) => {
      state.selectedDate = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch assignments
    builder
      .addCase(fetchAssignments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssignments.fulfilled, (state, action) => {
        state.loading = false;
        state.assignments = action.payload;
      })
      .addCase(fetchAssignments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Create assignment
    builder
      .addCase(createAssignment.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createAssignment.fulfilled, (state, action) => {
        state.createLoading = false;
        state.assignments.push(action.payload);
      })
      .addCase(createAssignment.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      });

    // Update assignment
    builder
      .addCase(updateAssignment.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateAssignment.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.assignments.findIndex(assignment => assignment.id === action.payload.id);
        if (index !== -1) {
          state.assignments[index] = action.payload;
        }
      })
      .addCase(updateAssignment.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      });

    // Delete assignment
    builder
      .addCase(deleteAssignment.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteAssignment.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.assignments = state.assignments.filter(assignment => assignment.id !== action.payload);
      })
      .addCase(deleteAssignment.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      });

    // Fetch employees
    builder
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.employees = action.payload;
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Fetch locations
    builder
      .addCase(fetchLocations.fulfilled, (state, action) => {
        state.locations = action.payload;
      })
      .addCase(fetchLocations.rejected, (state, action) => {
        state.error = action.payload;
      });
  }
});

export const {
  setFilters,
  clearFilters,
  setViewMode,
  setSelectedDate,
  clearError
} = assignmentSlice.actions;

// Selectors
export const selectAssignments = (state) => state.assignments.assignments;
export const selectEmployees = (state) => state.assignments.employees;
export const selectLocations = (state) => state.assignments.locations;
export const selectFilters = (state) => state.assignments.filters;
export const selectViewMode = (state) => state.assignments.viewMode;
export const selectSelectedDate = (state) => state.assignments.selectedDate;
export const selectLoading = (state) => state.assignments.loading;
export const selectError = (state) => state.assignments.error;
export const selectCreateLoading = (state) => state.assignments.createLoading;
export const selectUpdateLoading = (state) => state.assignments.updateLoading;
export const selectDeleteLoading = (state) => state.assignments.deleteLoading;

// Filtered assignments selector
export const selectFilteredAssignments = (state) => {
  const { assignments, filters, employees, locations } = state.assignments;
  
  return assignments.filter(assignment => {
    const employee = employees.find(emp => emp.id === assignment.employeeId);
    const location = locations.find(loc => loc.id === assignment.locationId);
    
    const matchesSearch = !filters.search || 
      (employee?.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
       location?.name?.toLowerCase().includes(filters.search.toLowerCase()));
    const matchesEmployee = filters.employee === 'all' || assignment.employeeId === filters.employee;
    const matchesLocation = filters.location === 'all' || assignment.locationId === filters.location;
    const matchesStatus = filters.status === 'all' || assignment.status === filters.status;
    
    // Date range filtering
    let matchesDateRange = true;
    if (filters.dateRange !== 'all') {
      const assignmentDate = new Date(assignment.startTime);
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      
      switch (filters.dateRange) {
        case 'today':
          matchesDateRange = assignmentDate.toDateString() === today.toDateString();
          break;
        case 'week':
          matchesDateRange = assignmentDate >= startOfWeek && assignmentDate <= endOfWeek;
          break;
        case 'month':
          matchesDateRange = assignmentDate.getMonth() === today.getMonth() && 
                           assignmentDate.getFullYear() === today.getFullYear();
          break;
        case 'next_week':
          const nextWeekStart = new Date(startOfWeek);
          nextWeekStart.setDate(startOfWeek.getDate() + 7);
          const nextWeekEnd = new Date(nextWeekStart);
          nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
          matchesDateRange = assignmentDate >= nextWeekStart && assignmentDate <= nextWeekEnd;
          break;
        case 'next_month':
          const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
          const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0);
          matchesDateRange = assignmentDate >= nextMonth && assignmentDate <= nextMonthEnd;
          break;
      }
    }
    
    return matchesSearch && matchesEmployee && matchesLocation && matchesStatus && matchesDateRange;
  });
};

export default assignmentSlice.reducer; 