import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authAPI } from '../../services/api';
import { decodeJWT } from '../../services/jwt';

// Demo credentials - now will try database first
const DEMO_CREDENTIALS = {
  '<EMAIL>': {
    password: 'admin123',
    user: {
      id: 'demo-admin-1',
      email: '<EMAIL>',
      name: 'Demo Admin',
      role: 'client_admin'
    },
    roles: ['client_admin'],
    tenant: 'demo-tenant-1'
  },
  '<EMAIL>': {
    password: 'admin123',
    user: {
      id: 'demo-super-1',
      email: '<EMAIL>',
      name: 'Demo Super Admin',
      role: 'super_admin'
    },
    roles: ['super_admin'],
    tenant: null
  }
};

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      // Try real API first
      const response = await authAPI.login(credentials);
      return response.data;
    } catch (error) {
      // If API fails, check if this is a demo login
      const demoUser = DEMO_CREDENTIALS[credentials.email];
      if (demoUser && demoUser.password === credentials.password) {
        console.log('API failed, using demo credentials for:', credentials.email);
        // Return demo token data
        return {
          token: 'demo-jwt-token-' + Date.now(),
          refreshToken: 'demo-refresh-token-' + Date.now(),
          user: demoUser.user,
          roles: demoUser.roles,
          tenant: demoUser.tenant,
          isDemo: true
        };
      }
      
      return rejectWithValue(error.response?.data || 'Login failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // For demo users, just clear local storage
      const token = localStorage.getItem('token');
      if (token && token.startsWith('demo-jwt-token')) {
        return null;
      }
      
      await authAPI.logout();
      return null;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Logout failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.refreshToken();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Token refresh failed');
    }
  }
);

function getInitialAuthState() {
  const token = localStorage.getItem('token');
  const refreshToken = localStorage.getItem('refreshToken');
  let user = null;
  let roles = [];
  let tenant = null;
  
  if (token) {
    // Handle demo tokens
    if (token.startsWith('demo-jwt-token')) {
      const userData = localStorage.getItem('demoUser');
      if (userData) {
        const parsedData = JSON.parse(userData);
        user = parsedData.user;
        roles = parsedData.roles;
        tenant = parsedData.tenant;
      }
    } else {
      // Handle real JWT tokens
      const decoded = decodeJWT(token);
      if (decoded) {
        user = decoded.user || null;
        roles = decoded.roles || [];
        tenant = decoded.tenant || decoded.tenantId || null;
      }
    }
  }
  
  return {
    user,
    token,
    refreshToken,
    isAuthenticated: !!token,
    isLoading: false,
    error: null,
    tenant,
    roles,
  };
}

const initialState = getInitialAuthState();

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setTenant: (state, action) => {
      state.tenant = action.payload;
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        
        // Handle demo vs real tokens
        if (action.payload.token.startsWith('demo-jwt-token')) {
          // Demo login - use provided data directly
          state.user = action.payload.user;
          state.roles = action.payload.roles;
          state.tenant = action.payload.tenant;
          
          // Store demo user data
          localStorage.setItem('demoUser', JSON.stringify({
            user: action.payload.user,
            roles: action.payload.roles,
            tenant: action.payload.tenant
          }));
        } else {
          // Real JWT - decode token
          const decoded = decodeJWT(action.payload.token);
          state.user = decoded?.user || null;
          state.roles = decoded?.roles || [];
          state.tenant = decoded?.tenant || decoded?.tenantId || null;
        }
        
        // Store tokens in localStorage
        localStorage.setItem('token', action.payload.token);
        localStorage.setItem('refreshToken', action.payload.refreshToken);
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.tenant = null;
        state.roles = [];
        // Clear tokens from localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('demoUser');
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Refresh token
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        localStorage.setItem('token', action.payload.token);
        localStorage.setItem('refreshToken', action.payload.refreshToken);
      });
  }
});

export const { clearError, setTenant, updateUser } = authSlice.actions;
export default authSlice.reducer; 