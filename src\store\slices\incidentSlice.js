import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { incidentAPI, userAPI, locationAPI } from '../../services/api';

// Async thunks
export const fetchIncidents = createAsyncThunk(
  'incidents/fetchIncidents',
  async ({ organizationId, filters = {} }, { rejectWithValue }) => {
    try {
      const response = await incidentAPI.getIncidents(organizationId, filters);

      // Transform backend data to frontend expected format
      const transformedData = response.data.map(incident => ({
        ...incident,
        locationId: incident.location?.id || null,
        assignedEmployeeId: incident.assignedEmployee?.id || null,
        reporterId: incident.reporter?.id || null,
        locationName: incident.location?.name || 'Unknown',
        employeeName: incident.assignedEmployee?.name || 'Unassigned',
        submittedBy: incident.reporter?.name || 'Unknown',
        submittedAt: incident.createdAt,
        attachments: incident.attachments || []
      }));

      return transformedData;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch incidents');
    }
  }
);

export const fetchEmployees = createAsyncThunk(
  'incidents/fetchEmployees',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUsers(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch employees');
    }
  }
);

export const fetchLocations = createAsyncThunk(
  'incidents/fetchLocations',
  async ({ organizationId }, { rejectWithValue }) => {
    try {
      const response = await locationAPI.getLocations(organizationId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch locations');
    }
  }
);

export const createIncident = createAsyncThunk(
  'incidents/createIncident',
  async ({ organizationId, incidentData }, { rejectWithValue }) => {
    try {
      const response = await incidentAPI.createIncident(organizationId, incidentData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create incident');
    }
  }
);

export const updateIncident = createAsyncThunk(
  'incidents/updateIncident',
  async ({ organizationId, incidentId, incidentData }, { rejectWithValue }) => {
    try {
      const response = await incidentAPI.updateIncident(organizationId, incidentId, incidentData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update incident');
    }
  }
);

export const deleteIncident = createAsyncThunk(
  'incidents/deleteIncident',
  async ({ organizationId, incidentId }, { rejectWithValue }) => {
    try {
      await incidentAPI.deleteIncident(organizationId, incidentId);
      return incidentId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete incident');
    }
  }
);

export const submitIncidentFromMobile = createAsyncThunk(
  'incidents/submitIncidentFromMobile',
  async ({ organizationId, employeeId, incidentData }, { rejectWithValue }) => {
    try {
      const response = await incidentAPI.createIncident(organizationId, {
        ...incidentData,
        submittedBy: employeeId,
        submittedAt: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit incident');
    }
  }
);

// Initial state
const initialState = {
  incidents: [],
  employees: [],
  locations: [],
  loading: false,
  error: null,
  filters: {
    search: '',
    status: 'all',
    severity: 'all',
    category: 'all',
    priority: 'all',
    location: 'all',
    employee: 'all',
    dateRange: 'all'
  },
  stats: {
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    closed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0
  }
};

// Slice
const incidentSlice = createSlice({
  name: 'incidents',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    updateIncidentStatus: (state, action) => {
      const { incidentId, status } = action.payload;
      const incident = state.incidents.find(inc => inc.id === incidentId);
      if (incident) {
        incident.status = status;
        incident.updatedAt = new Date().toISOString();
      }
    },
    addIncidentFromWebSocket: (state, action) => {
      const newIncident = action.payload;
      // Check if incident already exists
      const existingIndex = state.incidents.findIndex(inc => inc.id === newIncident.id);
      if (existingIndex >= 0) {
        state.incidents[existingIndex] = newIncident;
      } else {
        state.incidents.unshift(newIncident);
      }
    },
    updateIncidentFromWebSocket: (state, action) => {
      const updatedIncident = action.payload;
      const index = state.incidents.findIndex(inc => inc.id === updatedIncident.id);
      if (index >= 0) {
        state.incidents[index] = updatedIncident;
      }
    },
    calculateStats: (state) => {
      const incidents = state.incidents;
      state.stats = {
        total: incidents.length,
        open: incidents.filter(inc => inc.status === 'open').length,
        inProgress: incidents.filter(inc => inc.status === 'in_progress').length,
        resolved: incidents.filter(inc => inc.status === 'resolved').length,
        closed: incidents.filter(inc => inc.status === 'closed').length,
        critical: incidents.filter(inc => inc.severity === 'critical').length,
        high: incidents.filter(inc => inc.severity === 'high').length,
        medium: incidents.filter(inc => inc.severity === 'medium').length,
        low: incidents.filter(inc => inc.severity === 'low').length
      };
    }
  },
  extraReducers: (builder) => {
    // Fetch incidents
    builder
      .addCase(fetchIncidents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIncidents.fulfilled, (state, action) => {
        state.loading = false;
        state.incidents = action.payload;
        // Calculate stats after fetching incidents
        incidentSlice.caseReducers.calculateStats(state);
      })
      .addCase(fetchIncidents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch employees
    builder
      .addCase(fetchEmployees.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.employees = action.payload;
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Fetch locations
    builder
      .addCase(fetchLocations.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchLocations.fulfilled, (state, action) => {
        state.locations = action.payload;
      })
      .addCase(fetchLocations.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Create incident
    builder
      .addCase(createIncident.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createIncident.fulfilled, (state, action) => {
        state.loading = false;
        state.incidents.unshift(action.payload);
        incidentSlice.caseReducers.calculateStats(state);
      })
      .addCase(createIncident.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Update incident
    builder
      .addCase(updateIncident.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateIncident.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.incidents.findIndex(inc => inc.id === action.payload.id);
        if (index >= 0) {
          state.incidents[index] = action.payload;
        }
        incidentSlice.caseReducers.calculateStats(state);
      })
      .addCase(updateIncident.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Delete incident
    builder
      .addCase(deleteIncident.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteIncident.fulfilled, (state, action) => {
        state.loading = false;
        state.incidents = state.incidents.filter(inc => inc.id !== action.payload);
        incidentSlice.caseReducers.calculateStats(state);
      })
      .addCase(deleteIncident.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Submit incident from mobile
    builder
      .addCase(submitIncidentFromMobile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(submitIncidentFromMobile.fulfilled, (state, action) => {
        state.loading = false;
        state.incidents.unshift(action.payload);
        incidentSlice.caseReducers.calculateStats(state);
      })
      .addCase(submitIncidentFromMobile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Actions
export const {
  clearError,
  setFilters,
  clearFilters,
  updateIncidentStatus,
  addIncidentFromWebSocket,
  updateIncidentFromWebSocket,
  calculateStats
} = incidentSlice.actions;

// Selectors
export const selectIncidents = (state) => state.incidents.incidents;
export const selectEmployees = (state) => state.incidents.employees;
export const selectLocations = (state) => state.incidents.locations;
export const selectIncidentLoading = (state) => state.incidents.loading;
export const selectIncidentError = (state) => state.incidents.error;
export const selectIncidentFilters = (state) => state.incidents.filters;
export const selectIncidentStats = (state) => state.incidents.stats;

// Filtered incidents selector
export const selectFilteredIncidents = (state) => {
  const incidents = state.incidents.incidents;
  const filters = state.incidents.filters;
  
  return incidents.filter(incident => {
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch = 
        incident.title.toLowerCase().includes(searchLower) ||
        incident.description.toLowerCase().includes(searchLower) ||
        incident.category.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Status filter
    if (filters.status !== 'all' && incident.status !== filters.status) {
      return false;
    }

    // Severity filter
    if (filters.severity !== 'all' && incident.severity !== filters.severity) {
      return false;
    }

    // Category filter
    if (filters.category !== 'all' && incident.category !== filters.category) {
      return false;
    }

    // Priority filter
    if (filters.priority !== 'all' && incident.priority !== filters.priority) {
      return false;
    }

    // Location filter
    if (filters.location !== 'all' && incident.locationId !== filters.location) {
      return false;
    }

    // Employee filter
    if (filters.employee !== 'all') {
      if (filters.employee === 'unassigned' && incident.assignedEmployeeId) {
        return false;
      }
      if (filters.employee !== 'unassigned' && incident.assignedEmployeeId !== filters.employee) {
        return false;
      }
    }

    // Date range filter
    if (filters.dateRange !== 'all') {
      const incidentDate = new Date(incident.submittedAt);
      const now = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          if (incidentDate < today) return false;
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          if (incidentDate < weekAgo) return false;
          break;
        case 'month':
          const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
          if (incidentDate < monthAgo) return false;
          break;
        case 'quarter':
          const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
          if (incidentDate < quarterAgo) return false;
          break;
        case 'year':
          const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
          if (incidentDate < yearAgo) return false;
          break;
        default:
          break;
      }
    }

    return true;
  });
};

export default incidentSlice.reducer; 