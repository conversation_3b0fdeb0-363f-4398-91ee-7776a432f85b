import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { locationAPI } from '../../services/api';

// Async thunks
export const fetchLocations = createAsyncThunk(
  'locations/fetchLocations',
  async ({ organizationId, filters = {} }, { rejectWithValue }) => {
    try {
      const response = await locationAPI.getLocations(organizationId, filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch locations');
    }
  }
);

export const createLocation = createAsyncThunk(
  'locations/createLocation',
  async ({ organizationId, locationData }, { rejectWithValue }) => {
    try {
      const response = await locationAPI.createLocation(organizationId, locationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create location');
    }
  }
);

export const updateLocation = createAsyncThunk(
  'locations/updateLocation',
  async ({ organizationId, locationId, locationData }, { rejectWithValue }) => {
    try {
      const response = await locationAPI.updateLocation(organizationId, locationId, locationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update location');
    }
  }
);

export const deleteLocation = createAsyncThunk(
  'locations/deleteLocation',
  async ({ organizationId, locationId }, { rejectWithValue }) => {
    try {
      await locationAPI.deleteLocation(organizationId, locationId);
      return locationId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete location');
    }
  }
);

// Initial state
const initialState = {
  locations: [],
  loading: false,
  error: null,
  filters: {
    search: '',
    type: 'all',
    status: 'all'
  }
};

const locationSlice = createSlice({
  name: 'locations',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    updateLocationStatus: (state, action) => {
      const { locationId, status } = action.payload;
      const location = state.locations.find(loc => loc.id === locationId);
      if (location) {
        location.status = status;
        location.updatedAt = new Date().toISOString();
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLocations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLocations.fulfilled, (state, action) => {
        state.loading = false;
        state.locations = action.payload;
      })
      .addCase(fetchLocations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(createLocation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createLocation.fulfilled, (state, action) => {
        state.loading = false;
        state.locations.unshift(action.payload);
      })
      .addCase(createLocation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(updateLocation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateLocation.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.locations.findIndex(loc => loc.id === action.payload.id);
        if (index !== -1) {
          state.locations[index] = action.payload;
        }
      })
      .addCase(updateLocation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(deleteLocation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteLocation.fulfilled, (state, action) => {
        state.loading = false;
        state.locations = state.locations.filter(loc => loc.id !== action.payload);
      })
      .addCase(deleteLocation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError, setFilters, clearFilters, updateLocationStatus } = locationSlice.actions;
export const selectLocations = (state) => state.locations.locations;
export const selectLocationLoading = (state) => state.locations.loading;
export const selectLocationError = (state) => state.locations.error;
export const selectLocationFilters = (state) => state.locations.filters;
export const selectFilteredLocations = (state) => {
  const { locations } = state.locations;
  const { search, type, status } = state.locations.filters;
  return locations.filter(location => {
    if (search && !location.name.toLowerCase().includes(search.toLowerCase()) &&
        !location.address?.toLowerCase().includes(search.toLowerCase())) {
      return false;
    }
    if (type !== 'all' && location.type !== type) {
      return false;
    }
    if (status !== 'all' && location.status !== status) {
      return false;
    }
    return true;
  });
};
export const selectLocationById = (state, locationId) => 
  state.locations.locations.find(loc => loc.id === locationId);
export default locationSlice.reducer; 