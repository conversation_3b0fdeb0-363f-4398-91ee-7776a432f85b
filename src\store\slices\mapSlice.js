import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Async thunks
export const fetchLiveEmployees = createAsyncThunk(
  'map/fetchLiveEmployees',
  async ({ tenantId }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/tenants/${tenantId}/employees/live`);
      return response.data;
    } catch (error) {
      // For demo purposes, return mock data if API fails
      return [
        {
          id: 'emp1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          department: 'Operations',
          status: 'on_duty',
          currentAssignment: {
            locationName: 'Downtown Office',
            startTime: '2024-01-15T09:00:00Z',
            endTime: '2024-01-15T17:00:00Z',
            notes: 'Regular shift'
          }
        },
        {
          id: 'emp2',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          department: 'Warehouse',
          status: 'late',
          expectedTime: '2024-01-15T08:00:00Z',
          currentAssignment: {
            locationName: 'Warehouse Facility',
            startTime: '2024-01-15T08:00:00Z',
            endTime: '2024-01-15T16:00:00Z',
            notes: 'Warehouse operations'
          }
        },
        {
          id: 'emp3',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          department: 'Delivery',
          status: 'on_break',
          breakEndTime: '2024-01-15T12:30:00Z',
          currentAssignment: {
            locationName: 'Retail Store',
            startTime: '2024-01-15T10:00:00Z',
            endTime: '2024-01-15T18:00:00Z',
            notes: 'Delivery route'
          }
        },
        {
          id: 'emp4',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          phone: '+****************',
          department: 'Operations',
          status: 'out_of_zone',
          currentAssignment: {
            locationName: 'Downtown Office',
            startTime: '2024-01-15T09:00:00Z',
            endTime: '2024-01-15T17:00:00Z',
            notes: 'Field work'
          }
        },
        {
          id: 'emp5',
          name: 'David Brown',
          email: '<EMAIL>',
          phone: '+****************',
          department: 'Warehouse',
          status: 'offline',
          currentAssignment: null
        }
      ];
    }
  }
);

export const fetchEmployeeLocations = createAsyncThunk(
  'map/fetchEmployeeLocations',
  async ({ tenantId }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/tenants/${tenantId}/employees/locations`);
      return response.data;
    } catch (error) {
      // For demo purposes, return mock location data if API fails
      return [
        {
          employeeId: 'emp1',
          latitude: 40.7128,
          longitude: -74.0060,
          address: '123 Main St, New York, NY',
          lastUpdate: new Date().toISOString(),
          accuracy: 10
        },
        {
          employeeId: 'emp2',
          latitude: 40.7589,
          longitude: -73.9851,
          address: '456 Broadway, New York, NY',
          lastUpdate: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
          accuracy: 15
        },
        {
          employeeId: 'emp3',
          latitude: 40.7505,
          longitude: -73.9934,
          address: '789 5th Ave, New York, NY',
          lastUpdate: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
          accuracy: 8
        },
        {
          employeeId: 'emp4',
          latitude: 40.7614,
          longitude: -73.9776,
          address: '321 Park Ave, New York, NY',
          lastUpdate: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
          accuracy: 20
        }
        // emp5 is offline, so no location data
      ];
    }
  }
);

export const updateEmployeeStatus = createAsyncThunk(
  'map/updateEmployeeStatus',
  async ({ tenantId, employeeId, status, data }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/tenants/${tenantId}/employees/${employeeId}/status`, {
        status,
        ...data
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update employee status');
    }
  }
);

export const sendMessageToEmployee = createAsyncThunk(
  'map/sendMessageToEmployee',
  async ({ tenantId, employeeId, message }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/tenants/${tenantId}/employees/${employeeId}/message`, {
        message
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to send message');
    }
  }
);

const initialState = {
  employees: [],
  employeeLocations: [],
  filters: {
    status: 'all',
    department: 'all',
    search: ''
  },
  viewState: {
    longitude: -74.006,
    latitude: 40.7128,
    zoom: 10
  },
  selectedEmployee: null,
  loading: false,
  error: null,
  lastUpdate: null,
  autoRefresh: true,
  refreshInterval: 30000 // 30 seconds
};

const mapSlice = createSlice({
  name: 'map',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        status: 'all',
        department: 'all',
        search: ''
      };
    },
    setViewState: (state, action) => {
      state.viewState = action.payload;
    },
    setSelectedEmployee: (state, action) => {
      state.selectedEmployee = action.payload;
    },
    setAutoRefresh: (state, action) => {
      state.autoRefresh = action.payload;
    },
    setRefreshInterval: (state, action) => {
      state.refreshInterval = action.payload;
    },
    updateEmployeeLocation: (state, action) => {
      const { employeeId, location } = action.payload;
      const existingIndex = state.employeeLocations.findIndex(loc => loc.employeeId === employeeId);
      
      if (existingIndex !== -1) {
        state.employeeLocations[existingIndex] = {
          ...state.employeeLocations[existingIndex],
          ...location,
          lastUpdate: new Date().toISOString()
        };
      } else {
        state.employeeLocations.push({
          employeeId,
          ...location,
          lastUpdate: new Date().toISOString()
        });
      }
    },
    updateEmployeeStatusLocal: (state, action) => {
      const { employeeId, status, data } = action.payload;
      const employee = state.employees.find(emp => emp.id === employeeId);
      
      if (employee) {
        employee.status = status;
        if (data) {
          Object.assign(employee, data);
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch live employees
    builder
      .addCase(fetchLiveEmployees.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLiveEmployees.fulfilled, (state, action) => {
        state.loading = false;
        state.employees = action.payload;
        state.lastUpdate = new Date().toISOString();
      })
      .addCase(fetchLiveEmployees.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch employee locations
    builder
      .addCase(fetchEmployeeLocations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEmployeeLocations.fulfilled, (state, action) => {
        state.loading = false;
        state.employeeLocations = action.payload;
        state.lastUpdate = new Date().toISOString();
      })
      .addCase(fetchEmployeeLocations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Update employee status
    builder
      .addCase(updateEmployeeStatus.fulfilled, (state, action) => {
        const { employeeId, status, data } = action.payload;
        const employee = state.employees.find(emp => emp.id === employeeId);
        
        if (employee) {
          employee.status = status;
          if (data) {
            Object.assign(employee, data);
          }
        }
      })
      .addCase(updateEmployeeStatus.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Send message to employee
    builder
      .addCase(sendMessageToEmployee.rejected, (state, action) => {
        state.error = action.payload;
      });
  }
});

export const {
  setFilters,
  clearFilters,
  setViewState,
  setSelectedEmployee,
  setAutoRefresh,
  setRefreshInterval,
  updateEmployeeLocation,
  updateEmployeeStatusLocal,
  clearError
} = mapSlice.actions;

// Selectors
export const selectLiveEmployees = (state) => state.map.employees;
export const selectEmployeeLocations = (state) => state.map.employeeLocations;
export const selectFilters = (state) => state.map.filters;
export const selectViewState = (state) => state.map.viewState;
export const selectSelectedEmployee = (state) => state.map.selectedEmployee;
export const selectMapLoading = (state) => state.map.loading;
export const selectMapError = (state) => state.map.error;
export const selectLastUpdate = (state) => state.map.lastUpdate;
export const selectAutoRefresh = (state) => state.map.autoRefresh;
export const selectRefreshInterval = (state) => state.map.refreshInterval;

// Filtered employees selector
export const selectFilteredEmployees = (state) => {
  const { employees, filters } = state.map;
  
  return employees.filter(employee => {
    const matchesStatus = filters.status === 'all' || employee.status === filters.status;
    const matchesDepartment = filters.department === 'all' || employee.department === filters.department;
    const matchesSearch = !filters.search || 
      employee.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      employee.email.toLowerCase().includes(filters.search.toLowerCase());
    
    return matchesStatus && matchesDepartment && matchesSearch;
  });
};

// Employee with location selector
export const selectEmployeesWithLocations = (state) => {
  const employees = selectFilteredEmployees(state);
  const locations = selectEmployeeLocations(state);
  
  return employees.map(employee => ({
    ...employee,
    location: locations.find(loc => loc.employeeId === employee.id)
  }));
};

export default mapSlice.reducer; 