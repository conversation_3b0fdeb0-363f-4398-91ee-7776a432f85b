import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { messagingAPI } from '../../services/api';

// Async thunks
export const fetchConversations = createAsyncThunk(
  'messaging/fetchConversations',
  async ({ tenantId }, { rejectWithValue }) => {
    try {
      const response = await messagingAPI.getConversations(tenantId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch conversations');
    }
  }
);

export const fetchMessages = createAsyncThunk(
  'messaging/fetchMessages',
  async ({ tenantId, conversationId }, { rejectWithValue }) => {
    try {
      const response = await messagingAPI.getMessages(tenantId, { conversationId });
      return { conversationId, messages: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch messages');
    }
  }
);

export const sendMessage = createAsyncThunk(
  'messaging/sendMessage',
  async ({ tenantId, conversationId, content, senderId, attachments = [] }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('content', content);
      formData.append('senderId', senderId);
      formData.append('conversationId', conversationId);
      
      attachments.forEach((attachment, index) => {
        formData.append(`attachments`, attachment);
      });

      const response = await messagingAPI.sendMessage(tenantId, formData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to send message');
    }
  }
);

export const createConversation = createAsyncThunk(
  'messaging/createConversation',
  async ({ tenantId, participantId, initialMessage }, { rejectWithValue }) => {
    try {
      const response = await messagingAPI.createConversation(tenantId, {
        participantId,
        initialMessage
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create conversation');
    }
  }
);

export const markAsRead = createAsyncThunk(
  'messaging/markAsRead',
  async ({ tenantId, conversationId }, { rejectWithValue }) => {
    try {
      await messagingAPI.markConversationAsRead(tenantId, conversationId);
      return { conversationId };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark as read');
    }
  }
);

export const markMessageAsRead = createAsyncThunk(
  'messaging/markMessageAsRead',
  async ({ tenantId, conversationId, messageId }, { rejectWithValue }) => {
    try {
      await messagingAPI.markMessageAsRead(tenantId, messageId);
      return { conversationId, messageId };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark message as read');
    }
  }
);

// Initial state
const initialState = {
  conversations: [],
  currentConversation: null,
  messages: {},
  loading: false,
  error: null,
  typingUsers: {},
  unreadCount: 0,
  onlineUsers: []
};

// Slice
const messagingSlice = createSlice({
  name: 'messaging',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentConversation: (state, action) => {
      state.currentConversation = action.payload;
    },
    addMessage: (state, action) => {
      const { conversationId, message } = action.payload;
      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }
      state.messages[conversationId].push(message);
      
      // Update conversation's last message
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.lastMessage = message;
        conversation.unreadCount = (conversation.unreadCount || 0) + 1;
      }
      
      // Update unread count
      state.unreadCount = state.conversations.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
    },
    updateMessageStatus: (state, action) => {
      const { conversationId, messageId, status, timestamp } = action.payload;
      const messages = state.messages[conversationId];
      if (messages) {
        const message = messages.find(m => m.id === messageId);
        if (message) {
          if (status === 'delivered') {
            message.deliveredAt = timestamp;
          } else if (status === 'read') {
            message.readAt = timestamp;
          }
        }
      }
    },
    setTypingUser: (state, action) => {
      const { conversationId, userId, isTyping } = action.payload;
      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }
      
      if (isTyping) {
        if (!state.typingUsers[conversationId].includes(userId)) {
          state.typingUsers[conversationId].push(userId);
        }
      } else {
        state.typingUsers[conversationId] = state.typingUsers[conversationId].filter(id => id !== userId);
      }
    },
    updateUserOnlineStatus: (state, action) => {
      const { userId, online, lastSeen } = action.payload;
      const conversation = state.conversations.find(c => c.participant?.id === userId);
      if (conversation) {
        conversation.participant.online = online;
        conversation.participant.lastSeen = lastSeen;
      }
    },
    addConversation: (state, action) => {
      state.conversations.unshift(action.payload);
    },
    updateConversation: (state, action) => {
      const index = state.conversations.findIndex(c => c.id === action.payload.id);
      if (index >= 0) {
        state.conversations[index] = { ...state.conversations[index], ...action.payload };
      }
    },
    removeConversation: (state, action) => {
      state.conversations = state.conversations.filter(c => c.id !== action.payload);
      if (state.currentConversation?.id === action.payload) {
        state.currentConversation = null;
      }
    },
    clearMessages: (state, action) => {
      const conversationId = action.payload;
      delete state.messages[conversationId];
    },
    setOnlineUsers: (state, action) => {
      state.onlineUsers = action.payload;
    }
  },
  extraReducers: (builder) => {
    // Fetch conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload;
        state.unreadCount = action.payload.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch messages
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        const { conversationId, messages } = action.payload;
        state.messages[conversationId] = messages;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.error = action.payload;
      });

    // Send message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.loading = false;
        const { conversationId, message } = action.payload;
        if (!state.messages[conversationId]) {
          state.messages[conversationId] = [];
        }
        state.messages[conversationId].push(message);
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Create conversation
    builder
      .addCase(createConversation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createConversation.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations.unshift(action.payload);
        state.currentConversation = action.payload;
      })
      .addCase(createConversation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Mark as read
    builder
      .addCase(markAsRead.fulfilled, (state, action) => {
        const { conversationId } = action.payload;
        const conversation = state.conversations.find(c => c.id === conversationId);
        if (conversation) {
          conversation.unreadCount = 0;
        }
        state.unreadCount = state.conversations.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
      });

    // Mark message as read
    builder
      .addCase(markMessageAsRead.fulfilled, (state, action) => {
        const { conversationId, messageId } = action.payload;
        const messages = state.messages[conversationId];
        if (messages) {
          const message = messages.find(m => m.id === messageId);
          if (message) {
            message.readAt = new Date().toISOString();
          }
        }
      });
  }
});

// Actions
export const {
  clearError,
  setCurrentConversation,
  addMessage,
  updateMessageStatus,
  setTypingUser,
  updateUserOnlineStatus,
  addConversation,
  updateConversation,
  removeConversation,
  clearMessages,
  setOnlineUsers
} = messagingSlice.actions;

// Selectors
export const selectConversations = (state) => state.messaging.conversations;
export const selectCurrentConversation = (state) => state.messaging.currentConversation;
export const selectMessages = (state) => state.messaging.messages;
export const selectMessagingLoading = (state) => state.messaging.loading;
export const selectMessagingError = (state) => state.messaging.error;
export const selectTypingUsers = (state) => state.messaging.typingUsers;
export const selectUnreadCount = (state) => state.messaging.unreadCount;
export const selectOnlineUsers = (state) => state.messaging.onlineUsers;

// Message selectors
export const selectMessagesForConversation = (conversationId) => (state) => 
  state.messaging.messages[conversationId] || [];

export const selectTypingUsersForConversation = (conversationId) => (state) => 
  state.messaging.typingUsers[conversationId] || [];

export default messagingSlice.reducer; 