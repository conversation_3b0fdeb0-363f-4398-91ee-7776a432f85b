import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchOrganizationById } from '../store/slices/organizationSlice';

const TenantContext = createContext();

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    // Return a default tenant object if context is not available
    return {
      tenant: { name: 'OnTheMove Admin', id: 'default' },
      isLoading: false,
      error: null,
      theme: 'default'
    };
  }
  return context;
};

export const TenantProvider = ({ children }) => {
  const dispatch = useDispatch();
  const { user, tenant: authTenant } = useSelector((state) => state.auth);
  const { currentOrganization, isLoading, error } = useSelector((state) => state.organization);
  const [theme, setTheme] = useState('default');

  // Get organization ID from authenticated user or auth state
  const organizationId = authTenant || user?.organization || user?.organizationId || user?.tenant || user?.tenantId;

  useEffect(() => {
    if (organizationId && user?.role !== 'super_admin') {
      dispatch(fetchOrganizationById(organizationId));
    }
  }, [organizationId, dispatch, user?.role]);

  // Apply custom theme if available
  useEffect(() => {
    if (currentOrganization?.branding?.theme) {
      setTheme(currentOrganization.branding.theme);
      // TODO: apply theme (e.g., set CSS variables)
    }
  }, [currentOrganization]);

  // Provide default organization for super admin or when no organization is available
  const effectiveOrganization = currentOrganization || {
    id: user?.role === 'super_admin' ? 'system' : 'default',
    name: user?.role === 'super_admin' ? 'System Administration' : 'OnTheMove Admin',
    branding: { primaryColor: '#3b82f6' }
  };

  return (
    <TenantContext.Provider value={{
      tenant: effectiveOrganization, // keep key as tenant for compatibility
      isLoading,
      error,
      theme,
    }}>
      {children}
    </TenantContext.Provider>
  );
};